{"name": "mantine-minimal-next-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 8001", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit --strict", "test": "jest"}, "dependencies": {"@mantine/core": "8.0.2", "@mantine/form": "^8.0.2", "@mantine/hooks": "8.0.2", "@mantine/notifications": "^8.0.2", "@tabler/icons-react": "^3.31.0", "@tanstack/react-query": "^5.76.1", "@zxing/library": "^0.21.3", "axios": "^1.9.0", "cookies-next": "^5.1.0", "countries-list": "^3.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "form-data": "^4.0.2", "formidable": "^3.5.4", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jsqr": "^1.4.0", "leaflet": "^1.9.4", "next": "15.2.3", "next-auth": "4.22.1", "next-translate": "^2.5.3", "qrcode": "^1.5.4", "react": "19.1.0", "react-dom": "19.1.0", "react-leaflet": "^5.0.0", "react-qr-code": "^2.0.15", "use-debounce": "^10.0.5", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/file-saver": "^2", "@types/jest": "^29.5.8", "@types/leaflet": "^1.9.18", "@types/node": "22.13.11", "@types/qrcode": "^1.5.5", "@types/react": "19.0.12", "@types/react-dom": "19.0.4", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-next": "15.2.3", "eslint-config-prettier": "^8.9.0", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-sonarjs": "^0.19.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.4", "next-translate-plugin": "^2.5.3", "postcss": "^8.5.3", "postcss-preset-mantine": "1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "^2.7.1", "ts-jest": "^29.1.2", "typescript": "5.8.2"}, "packageManager": "yarn@4.9.1"}