import '@mantine/core/styles.css';
import '@mantine/notifications/styles.css';
import '../styles/ltr-pin-input.css';
import '../src/utils/date-format';
import Head from 'next/head';
import Providers from '../src/components/common/Providers';
import type { Session } from 'next-auth';
import { useRouter } from 'next/router';
import type { AppProps } from 'next/app';
import AppLayout from '../src/components/layouts/layout';
import ClientOnly from '../src/components/common/ClientOnly';
import LeafletCSS from '../src/components/common/LeafletCSS';
import { SessionProvider } from 'next-auth/react';
import { useLocaleRedirect } from '../src/hooks/useLocaleRedirect';
import { MaintenancePage } from '../src/components/maintenance-page';

const noLayoutRoutes = ['/', '/auth/login', '/auth/register', '/auth/verify-email', '/auth/forgot-password', '/auth/reset-password'];

export default function App({ Component, pageProps }: AppProps<{ session?: Session }>) {
  const router = useRouter();
  const { session, ...restPageProps } = pageProps;
  const shouldUseLayout = !noLayoutRoutes.includes(router.pathname);

  // Handle locale redirection
  useLocaleRedirect();

  if (
    process.env.NEXT_PUBLIC_MAINTENANCE_MODE === 'true'
    // && (url === '' || url === '')
  ) {
    return (
      <Providers>
        <MaintenancePage />
      </Providers>
    );
  }

  return (
    <SessionProvider session={session}>
      <Providers>
        <Head>
          <title>NAQALAT</title>
          <meta
            name="viewport"
            content="minimum-scale=1, initial-scale=1, width=device-width, user-scalable=no"
          />
          <link rel="shortcut icon" href="/favicon.svg" />
          <style>
            {`
          /* Font configuration */
          @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
          @import url('https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@200;300;400;500;600;700;800;900&display=swap');

          /* Font classes */
          .font-arabic, .font-arabic * {
            font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif !important;
          }

          .font-english, .font-english * {
            font-family: 'Roboto Condensed', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif !important;
          }

          /* Locale-specific font preferences */
          html[lang="ar"], html[lang="ar"] *, [dir="rtl"], [dir="rtl"] * {
            font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif !important;
          }

          html[lang="en"], html[lang="en"] *, [dir="ltr"], [dir="ltr"] * {
            font-family: 'Roboto Condensed', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif !important;
          }

          /* Force font on all elements */
          * {
            font-family: inherit !important;
          }

          /* RTL support */
          [dir="rtl"] {
            text-align: right;
          }

          [dir="rtl"] .mantine-Group-root {
            flex-direction: row-reverse;
          }

          /* Ensure header and menus appear above all content including maps */
          .mantine-AppShell-header {
            z-index: 9999 !important;
          }

          .mantine-AppShell-navbar {
            z-index: 9998 !important;
          }

          .mantine-Menu-dropdown {
            z-index: 10000 !important;
          }

          .mantine-Popover-dropdown {
            z-index: 10001 !important;
          }

          /* Specifically target notification dropdown */
          .mantine-Popover-dropdown[data-notification-dropdown] {
            z-index: 10500 !important;
            position: fixed !important;
            max-height: 80vh !important;
            overflow: hidden !important;
          }

          /* Ensure notification dropdown appears above header */
          [data-notification-dropdown] {
            z-index: 10500 !important;
          }

          /* Leaflet map containers should have lower z-index */
          .leaflet-container {
            z-index: 1 !important;
          }

          .leaflet-control-container {
            z-index: 2 !important;
          }

          /* Mantine notifications should be above everything */
          .mantine-Notifications-root {
            z-index: 11000 !important;
          }

          /* Ensure tooltips appear above other elements */
          .mantine-Tooltip-tooltip {
            z-index: 10600 !important;
          }

          /* Leaflet popup content transparent background */
          .leaflet-popup-content-wrapper {
            width: 420px !important;
            background-color: black !important;
            opacity: 0.9 !important;
            color: white !important;
          }
        `}
          </style>
        </Head>
        <ClientOnly loadingText="Initializing application...">
          <LeafletCSS />
          {shouldUseLayout ? (
            <AppLayout>
              <Component {...restPageProps} />
            </AppLayout>
          ) : (
            <Component {...restPageProps} />
          )}
        </ClientOnly>
      </Providers>
    </SessionProvider>

  );
}
