/* eslint-disable sonarjs/no-duplicate-string */
import { NextApiRequest, NextApiResponse } from 'next';
import { ZodError } from 'zod';
import { getJwt } from '../../src/utils';
import { BACKEND_API } from '../../src/lib/axios';
import { HTTP_CODE } from '../../src/data';
import {
  accessPointRequestSchemas,
} from '../../src/requests/access-point';
import {
  getAccessPointsApiResponseSchema,
  createAccessPointApiResponseSchema,
  updateAccessPointApiResponseSchema,
} from '../../src/requests/access-point/response-transformer';
import createApiError from '../../src/utils/create-api-error';

const apiMethods = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
} as const;

interface AccessPoint {
  id: string;
  business_name: string;
  address: string;
  geo_latitude: number;
  geo_longitude: number;
}

// Helper function to fetch a single page of access points
async function fetchAccessPointsPage(token: string, page: number): Promise<AccessPoint[]> {
  const pageResponse = await BACKEND_API.get(`/access-points?limit=100&page=${page}`, {
    headers: {
      Authorization: token,
    },
  });

  const { data } = pageResponse;
  if (data?.data?.access_operators) {
    return data.data.access_operators;
  }
  if (data?.access_operators) {
    return data.access_operators;
  }
  return [];
}

// Helper function to fetch all access points when limit > 100
async function fetchAllAccessPoints(token: string): Promise<AccessPoint[]> {
  const allAccessPoints: AccessPoint[] = [];
  const maxPages = 50; // Safety limit
  const promises: Promise<AccessPoint[]>[] = [];

  // Create promises for all pages up to maxPages
  for (let page = 0; page < maxPages; page += 1) {
    promises.push(fetchAccessPointsPage(token, page));
  }

  // Execute all requests in parallel and filter out empty results
  const results = await Promise.all(promises);
  const validResults = results.filter((result) => result.length > 0);

  // If we got fewer results than expected, we've reached the end
  if (validResults.length < maxPages) {
    validResults.forEach((result) => allAccessPoints.push(...result));
    return allAccessPoints;
  }

  // If all pages returned data, we might need more pages (but we'll limit to maxPages for safety)
  validResults.forEach((result) => allAccessPoints.push(...result));
  return allAccessPoints;
}

// Helper function to create response for all access points
function createAllAccessPointsResponse(allAccessPoints: AccessPoint[]) {
  return {
    success: true,
    message: 'Access operators retrieved successfully',
    data: {
      access_operators: allAccessPoints,
      pagination: {
        page: 0,
        limit: allAccessPoints.length,
        total: allAccessPoints.length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    },
  };
}

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Verify authentication
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Get query parameters with validation
    const { limit = '10', page = '0' } = req.query;
    const requestedLimit = parseInt(limit as string, 10) || 10;

    // If requesting a large limit, fetch all access points
    if (requestedLimit > 100) {
      const allAccessPoints = await fetchAllAccessPoints(token);
      const response = createAllAccessPointsResponse(allAccessPoints);
      // Validate and transform response using schema
      const validatedResponse = getAccessPointsApiResponseSchema.parse(response);
      return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
    }

    // For normal requests (limit <= 100), make a single request
    const validatedLimit = Math.min(Math.max(requestedLimit, 1), 100);
    const validatedPage = parseInt(page as string, 10) || 0;
    const queryString = `limit=${validatedLimit}&page=${validatedPage}`;

    const response = await BACKEND_API.get(`/access-points?${queryString}`, {
      headers: {
        Authorization: token,
      },
    });

    // Validate and transform response using schema
    const validatedResponse = getAccessPointsApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error: unknown) {
    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Validate and transform the request body using the access point schema
    const backendPayload = accessPointRequestSchemas.createAccessPointBackend.parse(req.body);

    const response = await BACKEND_API.post('/access-points', backendPayload, {
      headers: {
        Authorization: token,
      },
    });

    // Validate and transform response using schema
    const validatedResponse = createAccessPointApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (e) {
    // Handle Zod validation errors specifically
    if (e instanceof ZodError) {
      return res.status(HTTP_CODE.BAD_REQUEST).json({
        success: false,
        message: 'Validation error',
        errors: e.errors,
      });
    }

    const error = createApiError({ error: e });
    return res.status(error.code).json(error);
  }
}

async function handlePut(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Validate and transform the request body using the access point schema
    const backendPayload = accessPointRequestSchemas.updateAccessPointBackend.parse(req.body);

    const response = await BACKEND_API.put('/access-points', backendPayload, {
      headers: {
        Authorization: token,
      },
    });

    // Validate and transform response using schema
    const validatedResponse = updateAccessPointApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (e) {
    // Handle Zod validation errors specifically
    if (e instanceof ZodError) {
      return res.status(HTTP_CODE.BAD_REQUEST).json({
        success: false,
        message: 'Validation error',
        errors: e.errors,
      });
    }

    const error = createApiError({ error: e });
    return res.status(error.code).json(error);
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === apiMethods.GET) {
    return handleGet(req, res);
  }

  if (req.method === apiMethods.POST) {
    return handlePost(req, res);
  }

  if (req.method === apiMethods.PUT) {
    return handlePut(req, res);
  }

  res.setHeader('Allow', ['GET', 'POST', 'PUT']);
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
    success: false,
    message: `Method ${req.method} not allowed`,
  });
}
