import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import useTranslation from 'next-translate/useTranslation';
import {
  PasswordInput,
  Button,
  Paper,
  Title,
  Text,
  Container,
  Group,
  Anchor,
  Stack,
  LoadingOverlay,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { z } from 'zod';
import { resetPasswordRequest } from '../../src/requests/auth/calls';
import { notifications } from '@mantine/notifications';
import { resetPasswordSchema } from '../../src/requests';

type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

export default function ResetPasswordPage() {
  const router = useRouter();
  const { token } = router.query; // Get token from URL query

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { t } = useTranslation('auth');
  const { t: tCommon } = useTranslation('common');

  useEffect(() => {
    if (router.isReady && !token) {
      setError('No reset token found. Please use the link from your email.');
      notifications.show({
        title: tCommon('error'),
        message: tCommon('somethingWentWrong'),
        color: 'red',
      });
      // Optionally redirect
      // router.push('/auth/forgot-password');
    }
  }, [router.isReady, token, router]);

  const form = useForm<ResetPasswordFormValues>({
    validate: (values) => {
      const parsed = resetPasswordSchema.safeParse(values);
      const errors: Partial<Record<keyof ResetPasswordFormValues, string | null>> = {};

      if (!parsed.success) {
        parsed.error.errors.forEach((issue) => {
          const field = issue.path[0] as keyof ResetPasswordFormValues;
          errors[field] = issue.message;
        });
      }

      return errors as Record<keyof ResetPasswordFormValues, string | null>;
    },
    initialValues: {
      token: typeof token === 'string' ? token : '',
      password: '',
      confirmPassword: '',
    },
  });

  // Update form token field when the token from URL becomes available
  useEffect(() => {
    if (typeof token === 'string') {
      form.setFieldValue('token', token);
    }
  }, [token, form]);

  const handleSubmit = async (values: ResetPasswordFormValues) => {
    if (!token || typeof token !== 'string') {
      notifications.show({
        title: tCommon('error'),
        message: tCommon('somethingWentWrong'),
        color: 'red',
      });
      return;
    }
    setIsLoading(true);
    try {
      const response = await resetPasswordRequest({ data: { token: typeof token === 'string' ? token : values.token, password: values.password, confirmPassword: values.confirmPassword } });

      if (response.success) {
        notifications.show({
          title: tCommon('success'),
          message: response.message || t('passwordResetSuccess'),
          color: 'green',
        });
        router.push('/auth/login');
      } else {
        notifications.show({
          title: tCommon('error'),
          message: response.message || tCommon('somethingWentWrong'),
          color: 'red',
        });
      }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred. Please try again.';
      // error shape is already handled in calls.ts, just show generic message
      notifications.show({
        title: tCommon('error'),
        message: tCommon('somethingWentWrong'),
        color: 'red',
      });
    }
    setIsLoading(false);
  };

  if (error) {
    return (
      <Container size={420} my={40}>
        <Paper withBorder shadow="md" p={30} radius="md">
          <Title ta="center" c="red">Error</Title>
          <Text ta="center" mt="md">{error}</Text>
          <Group justify="center" mt="lg">
            <Link href="/auth/login" passHref legacyBehavior>
              <Anchor component="a" size="sm">
                Back to Sign In
              </Anchor>
            </Link>
          </Group>
        </Paper>
      </Container>
    );
  }

  if (!router.isReady) {
    return <LoadingOverlay visible />;
  }

  return (
    <Container size={420} my={40}>
      <Title ta="center">
        {t('resetPasswordTitle')}
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        {t('resetPasswordSubtitle')}
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        <LoadingOverlay visible={isLoading || !token} />
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack>
            <PasswordInput
              required
              label={t('newPassword')}
              placeholder={t('enterNewPassword')}
              {...form.getInputProps('password')}
            />
            <PasswordInput
              required
              label={t('confirmNewPassword')}
              placeholder={t('confirmNewPassword')}
              {...form.getInputProps('confirmPassword')}
            />
            <Button type="submit" fullWidth mt="xl" loading={isLoading} disabled={!token}>
              {tCommon('resetPassword')}
            </Button>
          </Stack>
        </form>
        <Group justify="center" mt="lg">
          <Link href="/auth/login" passHref legacyBehavior>
            <Anchor component="a" c="dimmed" size="sm">
              {t('backToSignIn')}
            </Anchor>
          </Link>
        </Group>
      </Paper>
    </Container>
  );
}
