import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import {
  Container,
  Title,
  Text,
  Paper,
  Grid,
  Card,
  Group,
  Badge,
  Button,
  Stack,
  Loader,
  Center,
} from '@mantine/core';
import {
  IconTruck,
  IconRoute,
  IconClock,
  IconMapPin,
  IconCheck,
} from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { getDashboardQuery } from '../../src/requests/dashboard/call';
import type {
  DashboardApiResponse,
  CarOperatorDashboardData,
  RecentShipment,
} from '../../src/requests/dashboard/type';

const formatNumber = (n?: number) => (typeof n === 'number' ? n.toLocaleString() : '—');

export default function CarOperatorDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { t } = useTranslation('dashboard');
  const { t: tCommon } = useTranslation('common');
  const isRTL = router.locale === 'ar';
  const {
    data: dashboard,
    isLoading: isDashboardLoading,
  } = useQuery<DashboardApiResponse>(getDashboardQuery({ enabled: status === 'authenticated' }));

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session) {
      router.push('/auth/login');
      return;
    }

    // Check if user is a car operator
    if (session.user?.user_type !== 'CAR_OPERATOR') {
      router.push('/'); // Redirect to home if not a car operator
    }
  }, [session, status, router]);

  if (status === 'loading' || isDashboardLoading) {
    return (
      <Center style={{ height: '100vh' }}>
        <Loader />
      </Center>
    );
  }

  if (!session || session.user?.user_type !== 'CAR_OPERATOR') {
    return null; // Will redirect
  }

  // Extract data
  const coData = dashboard?.data?.dashboardData as CarOperatorDashboardData | undefined;
  const shipmentStats = coData?.shipmentStats ?? {};
  const recentShipments = coData?.recentShipments ?? [];

  return (
    <Container size="xl" p="xl">
      <Stack gap="xl">
        {/* Header */}
        <div>
          <Title order={1} mb="sm">
            {t('carOperatorDashboard')}
          </Title>
          <Text size="lg" c="dimmed">
            {t('welcomeBack')}
            ,
            {' '}
            {session.user?.name || session.user?.email}
            !
            {' '}
            {t('manageDeliveries')}
          </Text>
        </div>

        {/* Quick Stats */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Text fw={500}>{t('activeDeliveriesText')}</Text>
                <IconTruck size="1.4rem" color="blue" />
              </Group>
              <Text size="xl" fw={700} c="blue">
                {formatNumber(shipmentStats.assigned)}
              </Text>
              <Text size="sm" c="dimmed">
                {t('currentlyAssigned')}
              </Text>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Text fw={500}>{t('completedToday')}</Text>
                <IconCheck size="1.4rem" color="green" />
              </Group>
              <Text size="xl" fw={700} c="green">
                {formatNumber(shipmentStats.completed)}
              </Text>
              <Text size="sm" c="dimmed">
                {t('deliveriesFinished')}
              </Text>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Text fw={500}>{t('pendingPickups')}</Text>
                <IconMapPin size="1.4rem" color="orange" />
              </Group>
              <Text size="xl" fw={700} c="orange">
                {formatNumber(shipmentStats.assigned && shipmentStats.completed !== undefined ? shipmentStats.assigned - shipmentStats.completed : undefined)}
              </Text>
              <Text size="sm" c="dimmed">
                {t('awaitingCollection')}
              </Text>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Text fw={500}>{t('totalDistance')}</Text>
                <IconRoute size="1.4rem" color="gray" />
              </Group>
              <Text size="xl" fw={700}>
                {shipmentStats.completionRate !== undefined ? `${shipmentStats.completionRate}%` : '—'}
              </Text>
              <Text size="sm" c="dimmed">
                {t('thisWeek')}
              </Text>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Quick Actions */}
        <Paper shadow="sm" p="lg" radius="md" withBorder>
          <Title order={3} mb="md">
            {t('quickActions')}
          </Title>
          <Group>
            <Button leftSection={<IconTruck size="1rem" />} size="md">
              {t('viewAvailableJobs')}
            </Button>
            <Button variant="outline" size="md">
              {t('updateLocation')}
            </Button>
            <Button variant="outline" size="md">
              {t('reportIssue')}
            </Button>
          </Group>
        </Paper>

        {/* Recent Shipments */}
        <Paper shadow="sm" p="lg" radius="md" withBorder>
          <Title order={3} mb="md">
            {t('recentShipments')}
          </Title>
          <Stack gap="md">
            {recentShipments.length === 0 && (
              <Text size="sm" c="dimmed">{t('noRecentShipments')}</Text>
            )}
            {recentShipments.slice(0, 5).map((s: RecentShipment) => (
              <Card key={s.id} shadow="xs" padding="md" radius="sm" withBorder>
                <Group justify="space-between" align="flex-start">
                  <div>
                    <Text fw={500} mb="xs">
                      {s.trackingCode}
                    </Text>
                    <Text size="sm" c="dimmed" mb="xs">
                      {t('status')}:
                      {' '}
                      {s.status}
                    </Text>
                    <Text size="sm" c="dimmed">
                      <IconClock
                        size="0.8rem"
                        style={{
                          display: 'inline',
                          [isRTL ? 'marginLeft' : 'marginRight']: '4px',
                        }}
                      />
                      {new Date(s.createdAt).toLocaleString()}
                    </Text>
                  </div>
                  <Badge color="blue" variant="light">
                    {s.status}
                  </Badge>
                </Group>
              </Card>
            ))}
          </Stack>
        </Paper>

        {/* Vehicle Status */}
        <Paper shadow="sm" p="lg" radius="md" withBorder>
          <Title order={3} mb="md">
            {t('vehicleStatus')}
          </Title>
          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Text fw={500} mb="xs">{t('vehicleInformation')}</Text>
              <Text size="sm" c="dimmed" mb="xs">{t('license')}: ABC-123</Text>
              <Text size="sm" c="dimmed" mb="xs">{t('type')}: {t('deliveryVan')}</Text>
              <Text size="sm" c="dimmed">{t('capacity')}: 1000 kg</Text>
            </Grid.Col>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Text fw={500} mb="xs">{t('currentStatus')}</Text>
              <Badge color="green" variant="light" size="lg" mb="xs">
                {t('available')}
              </Badge>
              <Text size="sm" c="dimmed">{t('lastUpdated')}: 2 {t('minutesAgo')}</Text>
            </Grid.Col>
          </Grid>
        </Paper>
      </Stack>
    </Container>
  );
}
