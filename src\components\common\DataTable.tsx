/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/require-default-props */
import React from 'react';
import {
  Table, Loader, Center, Text, Alert, Group, Pagination, Paper,
} from '@mantine/core';

export interface DataTableColumn<T> {
  label: string;
  accessor: keyof T | string;
  render?: (row: T) => React.ReactNode;
  sortable?: boolean;
}

export interface DataTableProps<T> {
  columns: DataTableColumn<T>[];
  data: T[];
  loading?: boolean;
  error?: string | null;
  emptyMessage?: string;
  pagination?: {
    page: number;
    totalPages: number;
    onPageChange: (page: number) => void;
  };
  rowActions?: (row: T) => React.ReactNode;
  onSortChange?: (sort: { accessor: string; direction: 'asc' | 'desc' }) => void;
  sortState?: { accessor: string; direction: 'asc' | 'desc' };
}

function getSortIcon(isSorted: boolean, direction?: 'asc' | 'desc') {
  if (!isSorted) return <span style={{ fontSize: 12, color: '#bbb' }}>⇅</span>;
  if (direction === 'asc') return <span style={{ fontSize: 12 }}>▲</span>;
  return <span style={{ fontSize: 12 }}>▼</span>;
}

export function DataTable<T>({
  columns,
  data,
  loading,
  error,
  emptyMessage = 'No data found',
  pagination,
  rowActions,
  onSortChange,
  sortState,
}: DataTableProps<T>) {
  if (loading) {
    return (
      <Center py="xl">
        <Loader size="lg" />
      </Center>
    );
  }
  if (error) {
    return (
      <Alert color="red" title="Error loading data" variant="light">
        {error}
      </Alert>
    );
  }
  if (!data || data.length === 0) {
    return (
      <Center py="xl">
        <Text c="dimmed">{emptyMessage}</Text>
      </Center>
    );
  }
  return (
    <Paper withBorder>
      <Table striped highlightOnHover style={{ direction: (typeof document !== 'undefined' ? document.documentElement.dir : 'ltr') as 'ltr' | 'rtl' }}>
        <Table.Thead>
          <Table.Tr>
            {columns.map((col) => {
              // Get the direction from the document or default to ltr
              const dir = typeof document !== 'undefined' ? document.documentElement.dir : 'ltr';
              const isRTL = dir === 'rtl';

              if (col.sortable) {
                const isSorted = sortState && sortState.accessor === col.accessor;
                const direction = isSorted ? sortState?.direction : undefined;
                return (
                  <Table.Th
                    key={col.accessor.toString()}
                    style={{
                      cursor: 'pointer',
                      userSelect: 'none',
                      textAlign: 'center',
                      direction: isRTL ? 'rtl' : 'ltr'
                    }}
                    onClick={() => {
                      if (!onSortChange) return;
                      let newDirection: 'asc' | 'desc' = 'asc';
                      if (isSorted && direction === 'asc') newDirection = 'desc';
                      onSortChange({ accessor: col.accessor.toString(), direction: newDirection });
                    }}
                  >
                    <Group gap={4} align="center" justify="center">
                      <span>{col.label}</span>
                      {getSortIcon(!!isSorted, direction)}
                    </Group>
                  </Table.Th>
                );
              }
              return (
                <Table.Th
                  key={col.accessor.toString()}
                  style={{
                    textAlign: 'center',
                    direction: isRTL ? 'rtl' : 'ltr'
                  }}
                >
                  {col.label}
                </Table.Th>
              );
            })}
            {rowActions && (
              <Table.Th style={{
                textAlign: 'center',
                direction: typeof document !== 'undefined' && document.documentElement.dir === 'rtl' ? 'rtl' : 'ltr'
              }}>
                {typeof document !== 'undefined' && document.documentElement.dir === 'rtl' ? 'الإجراءات' : 'Actions'}
              </Table.Th>
            )}
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {data.map((row, idx) => (
            <Table.Tr key={(row as any).id ?? idx}>
              {columns.map((col) => {
                // Get the direction from the document or default to ltr
                const dir = typeof document !== 'undefined' ? document.documentElement.dir : 'ltr';
                const isRTL = dir === 'rtl';

                return (
                  <Table.Td
                    key={col.accessor.toString()}
                    style={{
                      textAlign: 'center',
                      direction: isRTL ? 'rtl' : 'ltr'
                    }}
                  >
                    {col.render ? col.render(row) : (row as any)[col.accessor]}
                  </Table.Td>
                );
              })}
              {rowActions && (
                <Table.Td style={{
                  textAlign: 'center',
                  direction: typeof document !== 'undefined' && document.documentElement.dir === 'rtl' ? 'rtl' : 'ltr'
                }}>
                  {rowActions(row)}
                </Table.Td>
              )}
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
      {pagination && pagination.totalPages > 1 && (
        <Group justify="center" mt="md">
          <Pagination
            value={pagination.page}
            onChange={pagination.onPageChange}
            total={pagination.totalPages}
            size="sm"
          />
        </Group>
      )}
    </Paper>
  );
}
