/* eslint-disable no-restricted-syntax */
/* eslint-disable react/require-default-props */
import {
  useEffect, useMemo, useState, useRef,
} from 'react';
import {
  Group, Select, TextInput, Text, Box, Avatar,
} from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { useRouter } from 'next/router';
import { countries } from 'countries-list';
import getCountryFlag from '../../utils/get-country-flag';

interface PhoneNumberInputProps<T> {
  form: UseFormReturnType<T, (values: T) => T>;
  field: keyof T;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function PhoneNumberInput<T extends Record<string, any>>({
  form,
  field,
  label = 'Phone Number',
  placeholder,
  required = false,
  disabled = false,
}: PhoneNumberInputProps<T>) {
  const router = useRouter();
  const isRTL = router.locale === 'ar';

  const countryData = useMemo(() => {
    // Only show Syria (+963) for now
    const syriaCode = 'SY';
    const syriaData = countries[syriaCode];

    if (!syriaData) {
      // Fallback in case Syria data is not found
      return [{
        value: '+963',
        label: '+963',
        country: 'Syria',
        flagUrl: getCountryFlag('SY' as keyof typeof countries),
        code: 'SY',
      }];
    }

    const rawPhone = Array.isArray(syriaData.phone) ? syriaData.phone[0] : syriaData.phone;
    const dialCode = `+${rawPhone}`;
    const flagUrl = getCountryFlag(syriaCode as keyof typeof countries);

    return [{
      value: dialCode,
      label: `${dialCode}`,
      country: syriaData.name,
      flagUrl,
      code: syriaCode,
    }];
  }, []);

  const parsePhoneNumber = (phoneNumber: string) => {
    if (!phoneNumber || phoneNumber.trim() === '') {
      return { dialCode: '+963', local: '' };
    }

    const cleaned = phoneNumber.trim();

    // Try to match patterns with space separator first
    const spacePattern = /^(\+\d{1,4})\s+(.+)$/;
    const spaceMatch = cleaned.match(spacePattern);
    if (spaceMatch) {
      return { dialCode: spaceMatch[1], local: spaceMatch[2] };
    }

    // Try to match patterns without space
    // eslint-disable-next-line no-useless-escape
    const noSpacePattern = /^(\+\d{1,4})([0-9\s\-\(\)]+)$/;
    const noSpaceMatch = cleaned.match(noSpacePattern);
    if (noSpaceMatch) {
      return { dialCode: noSpaceMatch[1], local: noSpaceMatch[2].trim() };
    }

    // If it's just a dial code
    if (/^\+?\d{1,4}$/.test(cleaned)) {
      const dialCode = cleaned.startsWith('+') ? cleaned : `+${cleaned}`;
      return { dialCode, local: '' };
    }

    // Default: treat as local number with default dial code
    return { dialCode: '+963', local: cleaned };
  };

  // Get initial form value
  const formValue = (form.values[field as string] as string) ?? '';
  const initialParsed = parsePhoneNumber(formValue);

  const [dialCode, setDialCode] = useState<string>(initialParsed.dialCode);
  const [local, setLocal] = useState<string>(initialParsed.local);

  // Use ref to track if we're updating from internal changes
  const isInternalUpdate = useRef(false);
  const lastFormValue = useRef(formValue);

  // Find current country for display
  const currentCountry = countryData.find((country) => country.value === dialCode);

  // Only listen to external form changes (not our own updates)
  useEffect(() => {
    const currentFormValue = (form.values[field as string] as string) ?? '';

    // Skip if this is our own update or if the value hasn't actually changed
    if (isInternalUpdate.current || currentFormValue === lastFormValue.current) {
      isInternalUpdate.current = false;
      return;
    }

    // Only update if the form value changed externally
    if (currentFormValue !== lastFormValue.current) {
      const parsed = parsePhoneNumber(currentFormValue);
      setDialCode(parsed.dialCode);
      setLocal(parsed.local);
      lastFormValue.current = currentFormValue;
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.values[field as string]]);

  // Update form when local state changes
  const updateForm = (newDialCode: string, newLocal: string) => {
    const combined = newLocal ? `${newDialCode} ${newLocal}`.trim() : newDialCode;

    // Mark as internal update to prevent circular updates
    isInternalUpdate.current = true;
    lastFormValue.current = combined;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    form.setFieldValue(field as any, combined as any);
  };

  const handleDialCodeChange = (value: string | null) => {
    const newDialCode = value || '+963';
    setDialCode(newDialCode);
    updateForm(newDialCode, local);
  };

  const handleLocalChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newLocal = event.currentTarget.value;
    setLocal(newLocal);
    updateForm(dialCode, newLocal);
  };

  return (
    <Box>
      <Text size="sm" fw={500} mb={5}>
        {label}
        {required && <Text span c="red"> *</Text>}
      </Text>
      <Group gap="xs" wrap="nowrap" align="flex-start">
        {/* Always show Select (flag/code) on left, TextInput on right */}
        <Select
          data={countryData}
          searchable
          maxDropdownHeight={280}
          style={{ width: 130, flexShrink: 0 }}
          disabled={disabled}
          value={dialCode}
          onChange={handleDialCodeChange}
          styles={{
            dropdown: {
              padding: '4px',
            },
            option: {
              padding: '8px 12px',
              borderRadius: '6px',
              margin: '2px 4px',
            },
            input: {
              paddingLeft: currentCountry ? '40px' : '12px',
            },
          }}
          renderOption={({ option }) => {
            const country = countryData.find((c) => c.value === option.value);
            return (
              <Group gap="sm" wrap="nowrap" style={{ width: '100%' }}>
                <Avatar
                  src={country?.flagUrl}
                  alt={`${country?.country} flag`}
                  size="sm"
                  radius="sm"
                  style={{
                    width: 24,
                    height: 18,
                    flexShrink: 0,
                    border: '1px solid rgba(0,0,0,0.1)',
                  }}
                />
                <Box style={{ minWidth: 0, flex: 1, overflow: 'hidden' }}>
                  <Group gap="xs" justify="space-between" wrap="nowrap">
                    <Text size="sm" fw={500} style={{ whiteSpace: 'nowrap' }}>
                      {country?.value}
                    </Text>
                  </Group>
                </Box>
              </Group>
            );
          }}
          leftSection={
            currentCountry && (
              <Avatar
                src={currentCountry.flagUrl}
                alt={`${currentCountry.country} flag`}
                size="xs"
                radius="sm"
                style={{
                  width: 20,
                  height: 15,
                  border: '1px solid rgba(0,0,0,0.1)',
                  marginLeft: '4px',
                }}
              />
            )
          }
          comboboxProps={{
            styles: {
              dropdown: {
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
              },
            },
          }}
        />
        <TextInput
          style={{ flex: 1, minWidth: 0 }}
          placeholder={placeholder}
          disabled={disabled}
          value={local}
          error={form.errors[field as string]}
          onChange={handleLocalChange}
          onBlur={() => form.validateField(field as string)}
          type="tel"
          styles={{
            input: {
              borderRadius: '6px',
            },
          }}
        />
      </Group>
    </Box>
  );
}
