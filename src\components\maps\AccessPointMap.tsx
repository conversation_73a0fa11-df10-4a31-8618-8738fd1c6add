/* eslint-disable complexity */
/* eslint-disable react/require-default-props */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-underscore-dangle */
import { useEffect, useState, useRef } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMapEvents, useMap,
} from 'react-leaflet';
import { LatLngExpression, Icon } from 'leaflet';
import {
  Paper, Text, Button, Group, Stack, Alert, NumberInput,
} from '@mantine/core';
import { IconMapPin, IconCheck, IconAlertCircle } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import NoSSR from '../common/NoSSR';

// Fix for Leaflet default markers
delete (Icon.Default.prototype as any)._getIconUrl;
Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Fix for default markers in react-leaflet
const createDefaultIcon = () => {
  if (typeof window === 'undefined') return undefined;

  return new Icon({
    iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
    iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41],
  });
};

interface AccessPointMapProps {
  initialLat?: number;
  initialLng?: number;
  businessName?: string;
  address?: string;
  onLocationChange?: (lat: number, lng: number) => void;
  onSave?: (lat: number, lng: number) => void;
  isEditable?: boolean;
}

// Component to handle map clicks
function MapClickHandler({
  onPositionChange,
  isEditable,
}: {
  onPositionChange: (lat: number, lng: number) => void;
  isEditable: boolean;
}) {
  useMapEvents({
    click: (e: { latlng: { lat: any; lng: any; }; }) => {
      if (isEditable) {
        const { lat, lng } = e.latlng;
        onPositionChange(lat, lng);
      }
    },
  });
  return null;
}

// Component to control map view updates
function MapViewController({
  center,
  shouldAnimate = true,
}: {
  center: LatLngExpression | null;
  shouldAnimate?: boolean;
}) {
  const map = useMap();
  const prevCenterRef = useRef<LatLngExpression | null>(null);

  // eslint-disable-next-line sonarjs/cognitive-complexity
  useEffect(() => {
    if (center && map) {
      const lat = Array.isArray(center) ? center[0] : (center as any).lat;
      const lng = Array.isArray(center) ? center[1] : ((center as any).lng || (center as any).lon);

      // Check if the center has actually changed to avoid unnecessary updates
      const prevCenter = prevCenterRef.current;
      const hasChanged = !prevCenter
        || (Array.isArray(prevCenter) ? prevCenter[0] !== lat || prevCenter[1] !== lng
          : (prevCenter as any).lat !== lat || ((prevCenter as any).lng || (prevCenter as any).lon) !== lng);

      if (hasChanged) {
        // Update map view
        map.setView([lat, lng], map.getZoom(), {
          animate: shouldAnimate,
          duration: shouldAnimate ? 0.5 : 0, // Shorter animation for better UX
        });

        // Update ref
        prevCenterRef.current = center;
      }
    }
  }, [center, map, shouldAnimate]);

  return null;
}

// Component to display the location marker
function LocationMarker({
  position,
  defaultIcon,
}: {
  position: LatLngExpression | null;
  defaultIcon?: Icon;
}) {
  return position === null ? null : (
    <Marker position={position} {...(defaultIcon ? { icon: defaultIcon } : {})}>
      <Popup>
        <div>
          <Text size="sm" fw={500}>Access Point Location</Text>
          <Text size="xs" c="dimmed">
            Lat:
            {' '}
            {Array.isArray(position) ? position[0].toFixed(6) : (position as any).lat.toFixed(6)}
          </Text>
          <Text size="xs" c="dimmed">
            Lng:
            {' '}
            {Array.isArray(position) ? position[1].toFixed(6) : ((position as any).lng || (position as any).lon).toFixed(6)}
          </Text>
        </div>
      </Popup>
    </Marker>
  );
}

export default function AccessPointMap({
  initialLat, // No default location - user must select
  initialLng,
  businessName,
  address,
  onLocationChange,
  onSave,
  isEditable = true,
}: AccessPointMapProps) {
  const { t } = useTranslation('profile');

  const [position, setPosition] = useState<LatLngExpression | null>(
    (initialLat !== undefined && initialLng !== undefined)
      ? [initialLat, initialLng] as LatLngExpression
      : null,
  );
  const [manualLat, setManualLat] = useState<number>(initialLat || 34.8021); // Fallback for input fields only
  const [manualLng, setManualLng] = useState<number>(initialLng || 38.9968);
  const [isClient, setIsClient] = useState(false);
  const [defaultIcon, setDefaultIcon] = useState<Icon | undefined>(undefined);
  const [mapCenter, setMapCenter] = useState<LatLngExpression>(
    (initialLat !== undefined && initialLng !== undefined)
      ? [initialLat, initialLng]
      : [34.8021, 38.9968], // Default center for map display only
  );

  // Ensure this only renders on client side
  useEffect(() => {
    setIsClient(true);
    setDefaultIcon(createDefaultIcon());
  }, []);

  // Update position, manual inputs, and map center when initial coordinates change
  useEffect(() => {
    if (initialLat !== null && initialLat !== undefined && initialLng !== null && initialLng !== undefined) {
      const newPosition: LatLngExpression = [initialLat, initialLng];
      setPosition(newPosition);
      setManualLat(initialLat);
      setManualLng(initialLng);
      setMapCenter(newPosition);
    } else if (initialLat === null && initialLng === null) {
      setPosition(null);
      setManualLat(34.8021); // Reset to default
      setManualLng(38.9968); // Reset to default
      setMapCenter([34.8021, 38.9968]);
    }
  }, [initialLat, initialLng]);

  const handlePositionChange = (lat: number, lng: number) => {
    const newPosition: LatLngExpression = [lat, lng] as LatLngExpression;
    setPosition(newPosition);
    setManualLat(lat);
    setManualLng(lng);
    setMapCenter(newPosition); // Update map center
    onLocationChange?.(lat, lng);
  };

  const handleManualCoordinateChange = () => {
    // eslint-disable-next-line sonarjs/no-collapsible-if
    if (manualLat && manualLng && !Number.isNaN(manualLat) && !Number.isNaN(manualLng)) {
      // Validate coordinates are within valid ranges
      if (manualLat >= -90 && manualLat <= 90 && manualLng >= -180 && manualLng <= 180) {
        handlePositionChange(manualLat, manualLng);
      }
    }
  };

  const handleSave = () => {
    if (position) {
      const lat = Array.isArray(position) ? position[0] : position.lat;
      const lng = Array.isArray(position) ? position[1] : (position as any).lng || (position as any).lon;
      onSave?.(lat, lng);
    }
  };

  if (!isClient) {
    return <div>{t('loadingMap')}</div>;
  }

  return (
    <Stack gap="md">
      {businessName && (
        <Paper p="md" withBorder>
          <Group>
            <IconMapPin size="1.2rem" />
            <div>
              <Text fw={500}>{businessName}</Text>
              {address && <Text size="sm" c="dimmed">{address}</Text>}
            </div>
          </Group>
        </Paper>
      )}

      {isEditable && (
        <Alert icon={<IconAlertCircle size="1rem" />} color="blue">
          {t('clickMapToSetLocation')}
        </Alert>
      )}

      {/* Manual coordinate input */}
      {isEditable && (
        <Paper p="md" withBorder>
          <Text fw={500} mb="sm">{t('manualCoordinates')}</Text>
          <Group grow>
            <NumberInput
              label={t('latitude')}
              placeholder={t('enterLatitude')}
              value={manualLat}
              onChange={(value) => setManualLat(Number(value))}
              decimalScale={6}
              step={0.000001}
            />
            <NumberInput
              label={t('longitude')}
              placeholder={t('enterLongitude')}
              value={manualLng}
              onChange={(value) => setManualLng(Number(value))}
              decimalScale={6}
              step={0.000001}
            />
          </Group>
          <Button
            mt="sm"
            variant="outline"
            size="sm"
            onClick={handleManualCoordinateChange}
          >
            {t('setLocation')}
          </Button>
        </Paper>
      )}

      {/* Map */}
      <Paper withBorder style={{ height: '400px', overflow: 'hidden' }}>
        <NoSSR>
          <MapContainer
            {...({ center: mapCenter } as any)}
            zoom={13}
            style={{ height: '100%', width: '100%' }}
            key={`${initialLat}-${initialLng}`} // Force re-render when coordinates change
          >
            <TileLayer
              {...({ attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors' } as any)}
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <MapViewController center={position} />
            <MapClickHandler
              onPositionChange={handlePositionChange}
              isEditable={isEditable}
            />
            <LocationMarker
              position={position}
              defaultIcon={defaultIcon}
            />
          </MapContainer>
        </NoSSR>
      </Paper>

      {/* Current coordinates display */}
      {position && (
        <Paper p="md" withBorder>
          <Text fw={500} mb="xs">{t('currentLocation')}</Text>
          <Group>
            <Text size="sm">
              {t('latitude')}
              :
              {' '}
              {Array.isArray(position) ? position[0].toFixed(6) : (position as any).lat.toFixed(6)}
            </Text>
            <Text size="sm">
              {t('longitude')}
              :
              {' '}
              {Array.isArray(position) ? position[1].toFixed(6) : ((position as any).lng || (position as any).lon).toFixed(6)}
            </Text>
          </Group>
        </Paper>
      )}

      {/* Save button */}
      {isEditable && onSave && position && (
        <Group justify="flex-end">
          <Button
            leftSection={<IconCheck size="1rem" />}
            onClick={handleSave}
          >
            Save Location
          </Button>
        </Group>
      )}
    </Stack>
  );
}
