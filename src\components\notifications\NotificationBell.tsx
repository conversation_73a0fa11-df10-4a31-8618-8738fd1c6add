/* eslint-disable react/require-default-props */
import { ActionIcon, Indicator, Tooltip } from '@mantine/core';
import { IconBell } from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import { getNotificationsQuery } from '../../requests/notifications';

interface NotificationBellProps {
  onClick?: () => void;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'filled' | 'light' | 'outline' | 'subtle';
}

export default function NotificationBell({
  onClick,
  size = 'md',
  variant = 'subtle',
}: NotificationBellProps) {
  const { t } = useTranslation('notifications');

  // Fetch notifications to get unread count
  const { data: notificationData } = useQuery(
    getNotificationsQuery({
      params: { page: 0, limit: 50 },
      enabled: true,
    }),
  );

  const notifications = notificationData?.notifications || [];
  const unreadCount = notificationData?.unreadCount || 0;

  return (
    <Tooltip label={t('notifications')} position="bottom">
      <Indicator
        inline
        label={unreadCount > 99 ? '99+' : unreadCount}
        size={16}
        disabled={unreadCount === 0}
        color="red"
        offset={7}
      >
        <ActionIcon
          variant={variant}
          size={size}
          onClick={onClick}
          aria-label={t('notifications')}
        >
          <IconBell size="1.2rem" />
        </ActionIcon>
      </Indicator>
    </Tooltip>
  );
}
