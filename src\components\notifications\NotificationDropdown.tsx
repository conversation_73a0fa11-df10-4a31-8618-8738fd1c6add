/* eslint-disable react/require-default-props */
import React, { useState, useEffect } from 'react';
import {
  Popover,
  Box,
  Text,
  Group,
  Button,
  ScrollArea,
  Divider,
  Center,
  Loader,
  Stack,
  ActionIcon,
  Badge,
  Tooltip,
} from '@mantine/core';
import { IconBellRinging, IconCheck, IconEye } from '@tabler/icons-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import useTranslation from 'next-translate/useTranslation';
import {
  getNotificationsQuery,
  markAsReadMutationWithInvalidation,
  markAllAsReadMutationWithInvalidation,
  type Notification,
} from '../../requests/notifications';
import NotificationItem from './NotificationItem';
import NotificationBell from './NotificationBell';

interface NotificationDropdownProps {
  maxHeight?: number;
  maxItems?: number;
}

export default function NotificationDropdown({
  maxHeight = 400,
  maxItems = 10,
}: NotificationDropdownProps) {
  const { t } = useTranslation('notifications');
  const [opened, setOpened] = useState(false);
  const queryClient = useQueryClient();
  const router = useRouter();
  const { data: session } = useSession();

  // Fetch notifications when dropdown opens
  const {
    data: notificationData,
    isLoading,
    refetch,
  } = useQuery(
    getNotificationsQuery({
      params: { limit: maxItems, page: 0 },
      enabled: opened,
    }),
  );

  const notificationsList = notificationData?.notifications || [];
  const unreadCount = notificationData?.unreadCount || 0;

  // Mark as read mutation
  const markAsReadMutation = useMutation(
    markAsReadMutationWithInvalidation(queryClient),
  );

  // Mark all as read mutation
  const markAllAsReadMutation = useMutation(
    markAllAsReadMutationWithInvalidation(queryClient),
  );

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsReadMutation.mutateAsync({ notificationId });
    } catch (error) {
      //
    }
  };

  const handleMarkAllAsRead = async () => {
    if (unreadCount === 0) return;

    try {
      await markAllAsReadMutation.mutateAsync({});
      notifications.show({
        title: t('success'),
        message: t('allNotificationsMarkedAsRead'),
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: t('error'),
        message: t('failedToMarkAsRead'),
        color: 'red',
      });
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    // Close dropdown
    setOpened(false);

    // Navigate to appropriate page based on notification and user type
    if (notification.shipment_id) {
      const userType = session?.user?.user_type;

      // Navigate to appropriate shipment list page based on user type
      // The shipment detail will be shown within the list page
      if (userType === 'ACCESS_OPERATOR') {
        // For AO, navigate to their shipments page
        router.push('/access-operator/shipments/my');
      } else if (userType === 'CAR_OPERATOR') {
        // For CO, navigate to their shipments page
        router.push('/car-operator/shipments/my');
      } else if (userType === 'CUSTOMER') {
        // For customers, navigate to their shipments page
        router.push('/customer/shipments');
      } else {
        // Fallback to notifications page
        router.push('/notifications');
      }
    } else {
      // If no shipment_id, just navigate to notifications page
      router.push('/notifications');
    }
  };

  // Refetch when dropdown opens
  useEffect(() => {
    if (opened) {
      refetch();
    }
  }, [opened, refetch]);

  return (
    <Popover
      width={380}
      position="bottom-end"
      withArrow
      shadow="lg"
      opened={opened}
      onChange={setOpened}
      trapFocus={false}
      closeOnEscape
      closeOnClickOutside
      withinPortal
      zIndex={10500}
    >
      <Popover.Target>
        <Box>
          <NotificationBell onClick={() => setOpened((o) => !o)} />
        </Box>
      </Popover.Target>

      <Popover.Dropdown p={0} data-notification-dropdown>
        <Box>
          {/* Header */}
          <Group justify="space-between" p="md" pb="sm">
            <Group gap="xs">
              <IconBellRinging size="1.1rem" />
              <Text fw={600} size="md">
                {t('notifications')}
              </Text>
              {unreadCount > 0 && (
                <Badge size="sm" color="blue" variant="light">
                  {unreadCount}
                </Badge>
              )}
            </Group>

            {/* Always show mark all as read button for debugging */}
            <Tooltip label={t('markAllAsRead')} position="top">
              <ActionIcon
                variant="subtle"
                size="md"
                onClick={handleMarkAllAsRead}
                loading={markAllAsReadMutation.isPending}
                disabled={unreadCount === 0}
                style={{
                  opacity: unreadCount === 0 ? 0.5 : 1,
                  padding: '4px',
                }}
              >
                <IconCheck size="1.2rem" />
              </ActionIcon>
            </Tooltip>
          </Group>

          <Divider />

          {/* Content */}
          <Box style={{ maxHeight, minHeight: 200 }}>
            {(() => {
              if (isLoading) {
                return (
                  <Center py="xl">
                    <Stack align="center" gap="sm">
                      <Loader size="sm" />
                      <Text size="sm" c="dimmed">
                        {t('loadingNotifications')}
                      </Text>
                    </Stack>
                  </Center>
                );
              }

              if (notificationsList.length === 0) {
                return (
                  <Center py="xl">
                    <Stack align="center" gap="sm">
                      <IconBellRinging size="2rem" color="var(--mantine-color-gray-5)" />
                      <Text size="sm" c="dimmed" ta="center">
                        {t('noNotificationsMessage')}
                      </Text>
                    </Stack>
                  </Center>
                );
              }

              return (
                <ScrollArea mah={maxHeight - 60}>
                  <Stack gap={0}>
                    {notificationsList.map((notification, index) => (
                      <Box key={notification.id}>
                        <NotificationItem
                          notification={notification}
                          onMarkAsRead={handleMarkAsRead}
                          onClick={() => handleNotificationClick(notification)}
                          compact
                        />
                        {index < notificationsList.length - 1 && (
                          <Divider variant="dotted" />
                        )}
                      </Box>
                    ))}
                  </Stack>
                </ScrollArea>
              );
            })()}
          </Box>

          {/* Footer */}
          {notificationsList.length > 0 && (
            <>
              <Divider />
              <Box p="sm">
                <Button
                  component={Link}
                  href="/notifications"
                  variant="subtle"
                  size="sm"
                  fullWidth
                  leftSection={<IconEye size="1rem" />}
                  onClick={() => setOpened(false)}
                >
                  {t('viewAll')}
                </Button>
              </Box>
            </>
          )}
        </Box>
      </Popover.Dropdown>
    </Popover>
  );
}
