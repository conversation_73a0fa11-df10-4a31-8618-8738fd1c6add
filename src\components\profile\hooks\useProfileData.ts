/* eslint-disable no-console */
import { useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { useMemo } from 'react';
import { getProfileQuery } from '../../../requests/profile/calls';
import { CLIENT_API } from '../../../lib/axios';
import { API_ENDPOINT } from '../../../data';

// eslint-disable-next-line sonarjs/cognitive-complexity
export const useProfileData = () => {
  const { data: session } = useSession();

  // Debug session info
  if (process.env.NODE_ENV === 'development') {
    console.log('useProfileData session:', session);
    console.log('User type check:', {
      user_type: session?.user?.user_type,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      userType: (session?.user as any)?.userType,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      isCarOperator: session?.user?.user_type === 'CAR_OPERATOR' || (session?.user as any)?.userType === 'CAR_OPERATOR',
    });
  }

  // Get profile data
  const profileQuery = useQuery(
    getProfileQuery({ enabled: !!session }),
  );

  // Get access points for CO users
  const accessPointsQuery = useQuery({
    queryKey: ['access-points'],
    queryFn: () => CLIENT_API.get(`${API_ENDPOINT.accessPoints.list}?limit=500`)
      .then((res) => {
        const data = res?.data;
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.log('Access points API response:', data);
        }

        // Handle the actual API response structure
        // Check for transformed response (camelCase)
        if (data?.data?.accessOperators) {
          return data.data.accessOperators;
        }
        // Check for original response (snake_case)
        if (data?.data?.access_operators) {
          return data.data.access_operators;
        }
        if (data?.access_operators) {
          return data.access_operators;
        }
        if (data?.data?.access_points) {
          return data.data.access_points;
        }
        if (data?.access_points) {
          return data.access_points;
        }
        if (Array.isArray(data?.data)) {
          return data.data;
        }
        if (Array.isArray(data)) {
          return data;
        }
        return [];
      })
      .catch((error) => {
        if (process.env.NODE_ENV === 'development') {
          console.error('Access points API error:', error);
        }
        return [];
      }),
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    enabled: !!session && (session.user?.user_type === 'CAR_OPERATOR' || (session.user as any)?.userType === 'CAR_OPERATOR'),
    retry: 2,
    retryDelay: 1000,
  });

  // Memoize accessPointsData to prevent unnecessary re-renders
  const accessPointsData = useMemo(() => accessPointsQuery.data || [], [accessPointsQuery.data]);

  return {
    profileData: profileQuery.data,
    isLoading: profileQuery.isLoading,
    error: profileQuery.error,
    accessPointsData,
    session,
  };
};
