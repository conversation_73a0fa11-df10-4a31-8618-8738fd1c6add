import { UseFormReturnType } from '@mantine/form';

export interface ProfileFormValues {
  name: string;
  email: string;
  phone: string;
  address: string;
  businessName: string;
  licenseNumber: string;
  vehicleInfo: string;
  pickupAccessPointId: string;
  dropoffAccessPointId: string;
}

export interface ProfileUser {
  id: string;
  name: string;
  email: string;
  phone: string;
  userType: 'CUSTOMER' | 'ACCESS_OPERATOR' | 'CAR_OPERATOR' | 'ADMIN';
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING';
  businessName?: string;
  address?: string;
  geoLatitude?: number;
  geoLongitude?: number;
  licenseNumber?: string;
  vehicleInfo?: string;
  pickupAccessPointId?: string;
  dropoffAccessPointId?: string;
}

export interface ProfilePageProps {
  // Add any props if needed in the future
}

export interface ProfileFormProps {
  form: UseFormReturnType<ProfileFormValues>;
  isEditing: boolean;
  user: ProfileUser;
  userType: string;
  onSubmit: (values: ProfileFormValues) => void;
}

export interface ProfileHeaderProps {
  user: ProfileUser;
  userType: string;
}

export interface BasicInformationSectionProps {
  form: UseFormReturnType<ProfileFormValues>;
  isEditing: boolean;
  user: ProfileUser;
}

export interface GeoLocation {
  lat: number | null;
  lng: number | null;
}

export interface AccessOperatorSectionProps {
  form: UseFormReturnType<ProfileFormValues>;
  isEditing: boolean;
  geoLocation: GeoLocation;
  onLocationChange: (lat: number, lng: number) => void;
}

export interface CarOperatorSectionProps {
  form: UseFormReturnType<ProfileFormValues>;
  isEditing: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  accessPointsData: any[];
  transitPointIds: string[];
  onTransitPointsChange: (pointIds: string[]) => void;
}

export interface ProfileActionButtonsProps {
  isEditing: boolean;
  isLoading: boolean;
  onEdit: () => void;
  onCancel: () => void;
  onReset: () => void;
}

export interface AccessPoint {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  address?: string;
  businessName?: string;
}

// Hook types
export interface UseProfileFormProps {
  profileData?: {
    data?: {
      user?: ProfileUser;
    };
  };
  session?: {
    user?: {
      user_type?: string | null;
    };
  } | null;
  formOptions?: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    validate?: Record<string, (value: any) => string | null>;
    validateInputOnBlur?: boolean;
    validateInputOnChange?: boolean;
  };
}

export interface UseProfileFormReturn {
  form: UseFormReturnType<ProfileFormValues>;
  geoLocation: GeoLocation;
  transitPointIds: string[];
  handleLocationChange: (lat: number, lng: number) => void;
  handleTransitPointsChange: (pointIds: string[]) => void;
}
