/* eslint-disable linebreak-style */
import {
  <PERSON>ack,
  Text,
  Button,
  Alert,
  Tabs,
  TextInput,
  Group,
} from '@mantine/core';
import {
  IconQrcode,
  IconCamera,
  IconKeyboard,
  IconCheck,
} from '@tabler/icons-react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import QRScanner from '../../common/QRScanner';
import { QRCodeDisplay } from '../cards';

interface QRLabel {
  id: string;
  qr_value: string;
  status: string;
  shipment_id: string;
  assigned_at: string;
}

interface ShipmentQRStepProps {
  scannedQR: string;
  setScannedQR: (value: string) => void;
  qrLabel: QRLabel | null;
  handleGenerateQR: () => void;
  generateQRPending: boolean;
  scanMethod: 'camera' | 'manual';
  setScanMethod: (value: 'camera' | 'manual') => void;
  error: string | null;
  setError: (msg: string | null) => void;
  onNext: () => void;
  opened: boolean;
  activeStep: number;
}

export default function ShipmentQRStep({
  scannedQR,
  setScannedQR,
  qrLabel,
  handleGenerateQR,
  generateQRPending,
  scanMethod,
  setScanMethod,
  error,
  setError,
  onNext,
  opened,
  activeStep,
}: ShipmentQRStepProps) {
  const router = useRouter();
  const { t } = useTranslation('shipments');
  const isRTL = router.locale === 'ar';

  const validateShipmentQR = () => {
    if (!scannedQR) {
      setError(t('pleaseScanQRCode'));
      return false;
    }
    return true;
  };

  const clearAlerts = () => setError(null);

  return (
    <Stack gap="md">
      <Text fw={500}>Scan Shipment QR Code:</Text>

      {/* QR Generation Button: visible until a QR is successfully scanned */}
      {(!scannedQR || error) && (
        <Button
          variant="light"
          color="blue"
          leftSection={<IconQrcode size="1rem" />}
          onClick={handleGenerateQR}
          loading={generateQRPending}
          mb="md"
        >
          Generate New QR Code
        </Button>
      )}

      {qrLabel && (
        <Alert color="blue" variant="light" mb="md">
          <Stack gap="sm">
            <Text size="sm">Generated QR Code:</Text>
            <QRCodeDisplay
              value={qrLabel.qr_value}
              title="Shipment QR Code"
              description="Scan this QR code to assign the shipment"
              size={150}
              downloadFileName={`shipment-qr-${qrLabel.shipment_id?.slice?.(-8)}`}
            />
          </Stack>
        </Alert>
      )}

      {/* Show Tabs only until a QR code is successfully scanned (or an error occurs) */}
      {(!scannedQR || error) && (
        <Tabs value={scanMethod} onChange={(value) => setScanMethod(value as 'camera' | 'manual')}>
          <Tabs.List>
            <Tabs.Tab value="camera" leftSection={<IconCamera size="0.8rem" />}>Camera Scan</Tabs.Tab>
            <Tabs.Tab value="manual" leftSection={<IconKeyboard size="0.8rem" />}>Manual Entry</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="camera" pt="md">
            {/* Render scanner only until a QR is successfully scanned */}
            <QRScanner
              onScan={(val) => {
                clearAlerts();
                setScannedQR(val);
              }}
              onError={(e) => setError(e)}
              expectedFormat="AO_"
              title="Scan Shipment QR Code"
              description="Point your camera at the shipment QR code"
              isActive={scanMethod === 'camera' && opened && activeStep === 0}
            />
          </Tabs.Panel>

          <Tabs.Panel value="manual" pt="md">
            <TextInput
              label="Enter Shipment QR Code"
              placeholder="Enter the shipment QR code (e.g., AO_x7k9m2p1)"
              value={scannedQR}
              onChange={(event) => {
                clearAlerts();
                setScannedQR(event.currentTarget.value);
              }}
              required
              style={{ width: '100%' }}
              description="Enter the shipment QR code"
            />
          </Tabs.Panel>
        </Tabs>
      )}

      {/* Success alert displayed once a QR is scanned and no error present */}
      {scannedQR && !error && (
        <Alert icon={<IconCheck size="1rem" />} color="green" variant="light" mt="md">
          <Text size="sm">
            Shipment QR scanned
          </Text>
        </Alert>
      )}

      <Group justify="flex-end" mt="md">
        <Button
          onClick={() => {
            if (validateShipmentQR()) {
              onNext();
            }
          }}
        >
          Next Step
        </Button>
      </Group>
    </Stack>
  );
}
