import dynamic from 'next/dynamic';
import {
  <PERSON><PERSON>, Text, Divider, Loader, Center, Grid, Card, Stack, Group, Badge,
} from '@mantine/core';
import {
  IconTruck, IconFlag, IconMapPin, IconPhone, IconMail,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import { AccessPoint } from '../../../requests/access-point';

// Dynamically import the map component to avoid SSR issues
const CustomerAccessPointMap = dynamic(
  () => import('../../maps/CustomerAccessPointMap'),
  {
    ssr: false,
    loading: () => <Center style={{ height: '500px' }}><Loader /></Center>,
  },
);

// Map component's AccessPoint interface
interface MapAccessPoint {
  id: string;
  name: string;
  businessName?: string;
  address: string;
  geoLatitude: number;
  geoLongitude: number;
  phone?: string;
  email?: string;
}

interface AccessPointSelectionProps {
  accessPoints: AccessPoint[];
  selectedOrigin: AccessPoint | null;
  selectedDestination: AccessPoint | null;
  onOriginSelect: (accessPoint: AccessPoint) => void;
  onDestinationSelect: (accessPoint: AccessPoint) => void;
  isLoading: boolean;
}

// Component to display selected access point information
function SelectedAccessPointCard({ accessPoint, type }: { accessPoint: AccessPoint; type: 'origin' | 'destination' }) {
  const { t } = useTranslation('shipments');
  const isOrigin = type === 'origin';
  const icon = isOrigin ? <IconTruck size="1rem" color="green" /> : <IconFlag size="1rem" color="red" />;
  const title = isOrigin ? t('originAccessPoint') : t('destinationAccessPoint');
  const subtitle = isOrigin ? t('dropoffLocation') : t('pickupLocation');
  const badgeColor = isOrigin ? 'green' : 'red';

  return (
    <Card withBorder p="md" radius="md">
      <Stack gap="sm">
        <Group gap="xs">
          {icon}
          <div style={{ flex: 1 }}>
            <Group gap="xs" mb={4}>
              <Text fw={600} size="sm">{accessPoint.businessName || accessPoint.name}</Text>
              <Badge size="xs" color={badgeColor} variant="light">
                {title.split(' ')[0]}
              </Badge>
            </Group>
            <Text size="xs" c="dimmed">{subtitle}</Text>
          </div>
        </Group>

        {accessPoint.address && (
          <Group gap="xs">
            <IconMapPin size="0.7rem" color="gray" />
            <Text size="xs" c="dimmed">{accessPoint.address}</Text>
          </Group>
        )}

        <Group gap="md">
          {accessPoint.phone && (
            <Group gap="xs">
              <IconPhone size="0.7rem" color="gray" />
              <Text size="xs" c="dimmed" style={{ fontFamily: 'monospace' }}>
                {accessPoint.phone}
              </Text>
            </Group>
          )}
          {accessPoint.email && (
            <Group gap="xs">
              <IconMail size="0.7rem" color="gray" />
              <Text size="xs" c="dimmed" style={{ wordBreak: 'break-all' }}>
                {accessPoint.email}
              </Text>
            </Group>
          )}
        </Group>

        <Text size="xs" c="dimmed" style={{ fontStyle: 'italic' }}>
          {t('operatingHours')}: {t('operatingHoursTime')}
        </Text>
      </Stack>
    </Card>
  );
}

export function AccessPointSelection({
  accessPoints,
  selectedOrigin,
  selectedDestination,
  onOriginSelect,
  onDestinationSelect,
  isLoading,
}: AccessPointSelectionProps) {
  const { t } = useTranslation('shipments');

  const transformAccessPoint = (ap: AccessPoint): MapAccessPoint => ({
    id: ap.id,
    name: ap.name,
    businessName: ap.businessName || undefined,
    address: ap.address || '',
    geoLatitude: ap.geoLatitude || 0,
    geoLongitude: ap.geoLongitude || 0,
    phone: ap.phone || undefined,
    email: ap.email || undefined,
  });

  const mapAccessPoints = accessPoints.map(transformAccessPoint);

  // Handle selection callbacks - transform back to original AccessPoint
  const handleOriginSelect = (mapAp: MapAccessPoint) => {
    const originalAp = accessPoints.find((ap) => ap.id === mapAp.id);
    if (originalAp) {
      onOriginSelect(originalAp);
    }
  };

  const handleDestinationSelect = (mapAp: MapAccessPoint) => {
    const originalAp = accessPoints.find((ap) => ap.id === mapAp.id);
    if (originalAp) {
      onDestinationSelect(originalAp);
    }
  };

  return (
    <>
      {/* Access Points Map */}
      <CustomerAccessPointMap
        accessPoints={mapAccessPoints}
        selectedOriginId={selectedOrigin?.id}
        selectedDestinationId={selectedDestination?.id}
        onOriginSelect={handleOriginSelect}
        onDestinationSelect={handleDestinationSelect}
        isLoading={isLoading}
      />

      <Divider my="xl" />

      {/* Selected Access Points Summary */}
      {(selectedOrigin || selectedDestination) && (
        <>
          <Text fw={600} size="md" mb="md">Selected Access Points</Text>
          <Grid>
            {selectedOrigin && (
              <Grid.Col span={{ base: 12, md: 6 }}>
                <SelectedAccessPointCard accessPoint={selectedOrigin} type="origin" />
              </Grid.Col>
            )}
            {selectedDestination && (
              <Grid.Col span={{ base: 12, md: 6 }}>
                <SelectedAccessPointCard accessPoint={selectedDestination} type="destination" />
              </Grid.Col>
            )}
          </Grid>
        </>
      )}

      {/* Selection Status */}
      {(!selectedOrigin || !selectedDestination) && (
        <Alert color="blue" variant="light" mt="md">
          <Text size="sm">
            {!selectedOrigin && !selectedDestination
              && t('selectBothAccessPoints')}
            {selectedOrigin && !selectedDestination
              && t('selectDestinationAccessPoint')}
            {!selectedOrigin && selectedDestination
              && t('selectOriginAccessPoint')}
          </Text>
        </Alert>
      )}

    </>
  );
}
