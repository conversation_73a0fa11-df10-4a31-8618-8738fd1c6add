/* eslint-disable complexity */
/* eslint-disable react/require-default-props */
import { useState } from 'react';
import {
  Modal,
  Stack,
  Text,
  Button,
  Stepper,
  Paper,
  TextInput,
  Alert,
  Loader,
  Image,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  IconCheck,
  IconAlertTriangle,
  IconKeyboard,
} from '@tabler/icons-react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import { useMutation } from '@tanstack/react-query';
import { Shipment } from '../../../requests/shipment';
import { useDeliverShipmentMutation } from '../../../requests/hooks/enhanced-mutations';
import { useIsClient } from '../../../hooks/useIsClient';
import QRScanner from '../../common/QRScanner';

interface AOPickupModalProps {
  opened: boolean;
  onClose: () => void;
  shipment: Shipment | null;
  onSuccess?: () => void;
}

// TypeScript built-in type for geolocation
type GeolocationPosition = { coords: { latitude: number; longitude: number } };

export default function AOPickupModal({
  opened,
  onClose,
  shipment,
  onSuccess,
}: AOPickupModalProps) {
  const isClient = useIsClient();
  const router = useRouter();
  const { t } = useTranslation('shipments');

  const isRTL = router.locale === 'ar';

  // State management
  const [activeStep, setActiveStep] = useState(0);
  const [packageQR, setPackageQR] = useState(''); // AO QR from package
  const [pickupQR, setPickupQR] = useState(''); // Pickup QR from receiver
  const [receiverPhone, setReceiverPhone] = useState('');
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [photoUrl, setPhotoUrl] = useState<string | null>(null);
  const [notes, setNotes] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [scanMethod, setScanMethod] = useState<'camera' | 'manual'>('camera');
  const [loading, setLoading] = useState(false);
  const [photoUploading, setPhotoUploading] = useState(false);

  // Upload photo mutation
  const uploadPhotoMutation = useMutation({
    mutationFn: async (data: { photoBase64: string; geo_latitude?: number; geo_longitude?: number }) => {
      const response = await fetch('/api/uploads/photo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          photoBase64: data.photoBase64,
          folder: 'shipment-deliveries',
          geo_latitude: data.geo_latitude || null,
          geo_longitude: data.geo_longitude || null,
        }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload photo');
      }
      return response.json();
    },
    onError: (uploadError: Error) => {
      notifications.show({
        title: t('uploadFailed'),
        message: uploadError.message || t('failedUploadPhotoTryAgain'),
        color: 'red',
        icon: <IconAlertTriangle size="1rem" />,
        autoClose: 5000,
      });
    },
  });

  const handleClose = () => {
    setActiveStep(0);
    setPackageQR('');
    setPickupQR('');
    setReceiverPhone('');
    setPhotoPreview(null);
    setPhotoUrl(null);
    setNotes('');
    setError(null);
    setScanMethod('camera');
    setLoading(false);
    setPhotoUploading(false);
    onClose();
  };

  // Deliver shipment mutation with auto cache invalidation
  const deliverShipmentMutationInstance = useDeliverShipmentMutation({
    successMessage: t('pickupCompleteMessage'),
    onSuccess: () => {
      setActiveStep(4);
      setError(null);
      notifications.show({
        title: t('pickupComplete'),
        message: t('pickupCompleteMessage'),
        color: 'green',
        icon: <IconCheck size="1rem" />,
        autoClose: 5000,
      });
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
          handleClose();
        }, 2000);
      }
    },
    onError: (mutationError) => {
      const message = mutationError instanceof Error ? mutationError.message : 'Failed to complete delivery. Please try again.';
      setError(message);
      notifications.show({
        title: t('deliveryFailed'),
        message,
        color: 'red',
        icon: <IconAlertTriangle size="1rem" />,
        autoClose: 8000,
      });
    },
  });

  const handlePhotoChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64Data = e.target?.result as string;
        setPhotoPreview(base64Data);

        // Get geolocation (optional)
        let geoLatitude: number | undefined;
        let geoLongitude: number | undefined;
        try {
          if (navigator.geolocation) {
            const position = await new Promise<GeolocationPosition>((resolve, reject) => {
              navigator.geolocation.getCurrentPosition(resolve, reject, {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000,
              });
            });
            geoLatitude = position.coords.latitude;
            geoLongitude = position.coords.longitude;
          }
        } catch (geoError) {
          // Continue without geolocation
        }
        setPhotoUploading(true);
        try {
          const photoResponse = await uploadPhotoMutation.mutateAsync({
            photoBase64: base64Data,
            geo_latitude: geoLatitude,
            geo_longitude: geoLongitude,
          });
          setPhotoUrl(photoResponse.data.photo_url);
          setError(null);
        } catch {
          setPhotoUrl(null);
          setError(t('failedUploadPhotoTryAgain'));
        } finally {
          setPhotoUploading(false);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleConfirmDelivery = () => {
    if (!packageQR || !pickupQR || !photoUrl) {
      setError(t('allFieldsRequired'));
      return;
    }
    setLoading(true);
    deliverShipmentMutationInstance.mutate({
      data: {
        shipmentQr: packageQR,
        pickupQr: pickupQR,
        photoUrl,
        notes,
      },
    });
    setLoading(false);
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={t('deliverShipment')}
      size="lg"
      centered
      style={{ direction: isRTL ? 'rtl' : 'ltr' }}
    >
      <Stack>
        <Stepper active={activeStep} onStepClick={setActiveStep}>
          <Stepper.Step label={t('scanPackageQR')} description={t('scanQRCodeOnPackage')}>
            <Paper p="md" style={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>
              <Text mb="sm">{t('scanAOQRCodeAttached')}</Text>
              {scanMethod === 'camera' && isClient && (
                <QRScanner onScan={(value) => { setPackageQR(value); setError(null); }} onError={setError} isActive={scanMethod === 'camera' && opened && activeStep === 0} />
              )}
              <Button mt="sm" leftSection={<IconKeyboard size="1rem" />} variant="subtle" onClick={() => setScanMethod(scanMethod === 'camera' ? 'manual' : 'camera')}>
                {scanMethod === 'camera' ? t('enterCodeManually') : t('scanWithCamera')}
              </Button>
              {scanMethod === 'manual' && (
                <TextInput
                  label={t('packageQR')}
                  value={packageQR}
                  onChange={(e) => setPackageQR(e.currentTarget.value)}
                  placeholder={t('enterPackageQRCode')}
                  mt="sm"
                />
              )}
              <Button mt="md" onClick={() => setActiveStep(1)} disabled={!packageQR}>
                {t('next')}
              </Button>
            </Paper>
          </Stepper.Step>
          <Stepper.Step label={t('scanPickupQR')} description={t('scanPickupQRFromReceiver')}>
            <Paper p="md" style={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>
              <Text mb="sm">{t('scanPickupQRFromReceiver')}</Text>
              {scanMethod === 'camera' && isClient && (
                <QRScanner onScan={(value) => { setPickupQR(value); setError(null); }} onError={setError} isActive={scanMethod === 'camera' && opened && activeStep === 1} />
              )}
              <Button mt="sm" leftSection={<IconKeyboard size="1rem" />} variant="subtle" onClick={() => setScanMethod(scanMethod === 'camera' ? 'manual' : 'camera')}>
                {scanMethod === 'camera' ? t('enterCodeManually') : t('scanWithCamera')}
              </Button>
              {scanMethod === 'manual' && (
                <TextInput
                  label={t('pickupQR')}
                  value={pickupQR}
                  onChange={(e) => setPickupQR(e.currentTarget.value)}
                  placeholder={t('enterPickupQRCode')}
                  mt="sm"
                />
              )}
              <Button
                mt="md"
                onClick={() => setActiveStep(2)}
                disabled={!pickupQR}
                style={{ float: isRTL ? 'left' : 'right' }}
              >
                {t('next')}
              </Button>
            </Paper>
          </Stepper.Step>
          <Stepper.Step label={t('receiverInfo')} description={t('enterReceiverPhoneNumber')}>
            <Paper p="md" style={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>
              <TextInput
                label={t('receiverPhoneNumber')}
                value={receiverPhone}
                onChange={(e) => setReceiverPhone(e.currentTarget.value)}
                placeholder={t('enterReceiverPhoneNumber')}
                required
              />
              <Button
                mt="md"
                onClick={() => setActiveStep(3)}
                disabled={!receiverPhone}
                style={{ float: isRTL ? 'left' : 'right' }}
              >
                {t('next')}
              </Button>
            </Paper>
          </Stepper.Step>
          <Stepper.Step label={t('photoAndNotes')} description={t('uploadPhotoProofDelivery')}>
            <Paper p="md" style={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>
              <Text mb="sm">{t('uploadPhotoProofDelivery')}</Text>
              <input type="file" accept="image/*" onChange={handlePhotoChange} />
              {photoPreview && <Image src={photoPreview} alt="Preview" height={120} mt="sm" />}
              <TextInput
                label={t('notesOptional')}
                value={notes}
                onChange={(e) => setNotes(e.currentTarget.value)}
                placeholder={t('anyAdditionalNotes')}
                mt="sm"
              />
              <Button
                mt="md"
                onClick={handleConfirmDelivery}
                loading={loading}
                disabled={!photoUrl || !packageQR || !pickupQR || photoUploading}
                style={{ float: isRTL ? 'left' : 'right' }}
              >
                {t('confirmDelivery')}
              </Button>
            </Paper>
          </Stepper.Step>
          <Stepper.Completed>
            <Stack align="center" style={{ textAlign: isRTL ? 'right' : 'left' }}>
              <IconCheck size="3rem" color="green" />
              <Text fw={600} size="lg">{t('pickupComplete')}</Text>
              <Button mt="md" onClick={handleClose}>{t('close')}</Button>
            </Stack>
          </Stepper.Completed>
        </Stepper>
        {error && (
          <Alert
            color="red"
            icon={<IconAlertTriangle size="1rem" />}
            style={{ textAlign: isRTL ? 'right' : 'left' }}
          >
            {error}
          </Alert>
        )}
        {loading && <Loader />}
      </Stack>
    </Modal>
  );
}
