/* eslint-disable max-lines */
/* eslint-disable no-nested-ternary */
/* eslint-disable complexity */
import { useState } from 'react';
import {
  Modal,
  Stack,
  Group,
  Text,
  Button,
  Stepper,
  Paper,
  Box,
  TextInput,
  Textarea,
  Alert,
  Image,
  Loader,
  Tabs,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  IconQrcode,
  IconCamera,
  IconCheck,
  IconAlertTriangle,
  IconKeyboard,
} from '@tabler/icons-react';
import { useMutation } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import { Shipment } from '../../../requests/shipment';
import { useIsClient } from '../../../hooks/useIsClient';
import QRScanner from '../../common/QRScanner';
import { useScanShipmentMutation } from '../../../requests/hooks/enhanced-mutations';

interface COAssignToAOModalProps {
  opened: boolean;
  onClose: () => void;
  shipment: Shipment | null;
  // eslint-disable-next-line react/require-default-props
  onSuccess?: () => void;
}

export default function COAssignToAOModal({
  opened,
  onClose,
  shipment,
  onSuccess,
}: COAssignToAOModalProps) {
  const { t } = useTranslation('shipments');
  const isClient = useIsClient();
  const [activeStep, setActiveStep] = useState(0);
  const [scannedQR, setScannedQR] = useState('');
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [photoUrl, setPhotoUrl] = useState<string | null>(null);
  const [notes, setNotes] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [scanMethod, setScanMethod] = useState<'camera' | 'manual'>('camera');
  const [forceRestart, setForceRestart] = useState(0);

  // Upload photo mutation
  const uploadPhotoMutation = useMutation({
    mutationFn: async (data: { photoBase64: string }) => {
      const response = await fetch('/api/uploads/photo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          photoBase64: data.photoBase64,
          folder: 'shipment-scans',
        }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload photo');
      }
      return response.json();
    },
    onError: (uploadError: Error) => {
      notifications.show({
        title: 'Upload Failed',
        message: uploadError.message || 'Failed to upload photo. Please try again.',
        color: 'red',
        icon: <IconAlertTriangle size="1rem" />,
        autoClose: 5000,
      });
    },
  });

  const handleClose = () => {
    setActiveStep(0);
    setScannedQR('');
    setPhotoPreview(null);
    setPhotoUrl(null);
    setNotes('');
    setError(null);
    setScanMethod('camera');
    setForceRestart(0);
    onClose();
  };

  // Scan shipment mutation (ARRIVAL action) - using enhanced hook with auto-invalidation
  const scanShipmentMutationInstance = useScanShipmentMutation({
    successMessage: 'Package has been marked as arrived at the destination AO.',
    showNotifications: false, // We'll handle notifications manually for custom timing
    onSuccess: () => {
      setActiveStep(3);
      setError(null);
      notifications.show({
        title: 'Arrival Confirmed',
        message: 'Package has been marked as arrived at the destination AO.',
        color: 'green',
        icon: <IconCheck size="1rem" />,
        autoClose: 5000,
      });
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
          handleClose();
        }, 2000);
      }
    },
  });

  const handleQRScan = (value: string) => {
    setScannedQR(value);
    setError(null);
  };

  const handleProceedToPhoto = () => {
    if (!scannedQR) {
      setError('Please scan or enter a QR code before proceeding');
      return;
    }
    if (!scannedQR.startsWith('AO_')) {
      setError('Invalid QR code format. QR code must start with "AO_" (e.g., AO_x7k9m2p1)');
      return;
    }
    setActiveStep(1);
  };

  const handlePhotoChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64Data = e.target?.result as string;
        setPhotoPreview(base64Data);
        try {
          const photoResponse = await uploadPhotoMutation.mutateAsync({
            photoBase64: base64Data,
          });
          setPhotoUrl(photoResponse.data.photo_url);
          setError(null);
        } catch (uploadError) {
          setError('Failed to upload photo. Please try again.');
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleScanAndAssign = async () => {
    if (!photoUrl || !scannedQR) {
      setError('Please complete all required fields');
      return;
    }
    try {
      await scanShipmentMutationInstance.mutateAsync({
        data: {
          shipmentId: shipment?.id as string,
          qrValue: scannedQR,
          photoUrl,
          action: 'ARRIVAL',
          notes: notes || 'Package arrived at destination',
        },
      });
    } catch {
      // Error handled by mutation
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={t('markPackageArrivedDestinationAO')}
      size="lg"
      centered
    >
      <Stack gap="md">
        {error && (
          <Alert
            icon={<IconAlertTriangle size="1rem" />}
            color="red"
            variant="light"
            onClose={() => setError(null)}
            withCloseButton
          >
            {error}
          </Alert>
        )}
        <Stepper active={activeStep}>
          <Stepper.Step
            label={t('scanCode')}
            description={t('scanAOCodeOnPackage')}
            icon={<IconQrcode size="1.1rem" />}
          >
            <Paper p="md" withBorder>
              <Stack gap="md">
                <Text ta="center">
                  {t('markPackageArrivedForShipment')}
                  {' '}
                  <strong>{shipment?.trackingCode || shipment?.id?.slice(-8)}</strong>
                </Text>
                {!scannedQR && (
                  <>
                    <Alert color="blue" variant="light">
                      <Text size="sm">
                        <strong>{t('important')}:</strong>
                        {' '}
                        {t('onlyAOCodesCanBeUsed')}
                      </Text>
                    </Alert>
                    <Text size="sm" fw={500}>{t('scanCode')}</Text>
                    <Text size="xs" c="dimmed">
                      {t('scanCodeAttachedFromOriginAO')}
                    </Text>
                    <Tabs
                      value={scanMethod}
                      onChange={(value) => {
                        setScanMethod(value as 'camera' | 'manual');
                        if (value === 'camera' && error) {
                          setForceRestart((prev) => prev + 1);
                          setError(null);
                        }
                      }}
                    >
                      <Tabs.List>
                        <Tabs.Tab value="camera" leftSection={<IconCamera size="0.8rem" />}>
                          {t('cameraScan')}
                        </Tabs.Tab>
                        <Tabs.Tab value="manual" leftSection={<IconKeyboard size="0.8rem" />}>
                          {t('manualEntry')}
                        </Tabs.Tab>
                      </Tabs.List>
                      <Tabs.Panel value="camera" pt="md">
                        {(!scannedQR || error) && (
                          <QRScanner
                            key={forceRestart > 0 ? `camera-scanner-${forceRestart}` : 'camera-scanner'}
                            onScan={handleQRScan}
                            onError={(errorMsg) => setError(errorMsg)}
                            expectedFormat="AO_"
                            title={t('scanCode')}
                            description={t('pointCameraAtCodeOnPackage')}
                            isActive={scanMethod === 'camera' && opened && activeStep === 0}
                          />
                        )}
                      </Tabs.Panel>
                      <Tabs.Panel value="manual" pt="md">
                        <TextInput
                          label={t('enterCode')}
                          placeholder={t('enterCodePlaceholder')}
                          value={scannedQR}
                          onChange={(event) => setScannedQR(event.currentTarget.value)}
                          required
                          description={t('enterCodeFromPackage')}
                        />
                      </Tabs.Panel>
                    </Tabs>
                  </>
                )}
                {scannedQR && (
                  <Alert
                    icon={<IconCheck size="1rem" />}
                    color="green"
                    variant="light"
                    mt="md"
                  >
                    <Text size="sm">{t('codeScannedSuccessfully')}</Text>
                  </Alert>
                )}
                <Button
                  onClick={handleProceedToPhoto}
                  disabled={!scannedQR}
                  leftSection={<IconCamera size="1rem" />}
                  fullWidth
                >
                  {t('nextTakePhoto')}
                </Button>
              </Stack>
            </Paper>
          </Stepper.Step>
          <Stepper.Step
            label={t('takePhoto')}
            description={t('photoOfPackageAtDestination')}
            icon={<IconCamera size="1.1rem" />}
          >
            <Paper p="md" withBorder>
              <Stack gap="md">
                <Text size="sm" c="dimmed">
                  {t('takePhotoWithCodeVisible')}
                </Text>
                <input
                  type="file"
                  accept="image/*"
                  capture="environment"
                  onChange={handlePhotoChange}
                  style={{ display: 'none' }}
                  id="photo-input"
                />
                <Button
                  component="label"
                  htmlFor="photo-input"
                  leftSection={<IconCamera size="1rem" />}
                  variant="light"
                  fullWidth
                  loading={uploadPhotoMutation.isPending}
                  disabled={uploadPhotoMutation.isPending}
                >
                  {uploadPhotoMutation.isPending ? 'Uploading Photo...' : 'Take Photo of Package'}
                </Button>
                {photoPreview && (
                  <Box>
                    <Text size="sm" c="dimmed" mb="xs">Package Photo:</Text>
                    <Image
                      src={photoPreview}
                      alt="Package photo"
                      height={200}
                      fit="contain"
                      radius="md"
                    />
                    {photoUrl ? (
                      <Alert
                        icon={<IconCheck size="1rem" />}
                        color="green"
                        variant="light"
                        mt="md"
                      >
                        Photo uploaded successfully!
                      </Alert>
                    ) : uploadPhotoMutation.isPending ? (
                      <Alert
                        icon={<Loader size="1rem" />}
                        color="blue"
                        variant="light"
                        mt="md"
                      >
                        {t('uploadingPhoto')}
                      </Alert>
                    ) : null}
                    {photoUrl && (
                      <Button
                        onClick={() => setActiveStep(2)}
                        leftSection={<IconCheck size="1rem" />}
                        fullWidth
                        mt="md"
                      >
                        {t('continueToAssignment')}
                      </Button>
                    )}
                  </Box>
                )}
              </Stack>
            </Paper>
          </Stepper.Step>
          <Stepper.Step
            label={t('assign')}
            description={t('completeArrivalAssignment')}
            icon={<IconCheck size="1.1rem" />}
          >
            <Paper p="md" withBorder>
              <Stack gap="md">
                <Text size="sm" c="dimmed">
                  {t('reviewCompleteArrivalAssignment')}
                </Text>
                <Group>
                  <Text size="sm" fw={500}>QR Code:</Text>
                  <Text size="sm">{scannedQR}</Text>
                </Group>
                {photoPreview && (
                  <Box>
                    <Text size="sm" c="dimmed" mb="xs">Package Photo:</Text>
                    <Image
                      src={photoPreview}
                      alt="Package photo"
                      height={150}
                      fit="contain"
                      radius="md"
                    />
                  </Box>
                )}
                <Textarea
                  label={t('notesOptional')}
                  placeholder={t('addNotesPackageCondition')}
                  value={notes}
                  onChange={(event) => setNotes(event.currentTarget.value)}
                  rows={3}
                />
                <Button
                  onClick={handleScanAndAssign}
                  loading={scanShipmentMutationInstance.isPending}
                  leftSection={<IconCheck size="1rem" />}
                  color="green"
                  disabled={!scannedQR || !photoUrl}
                  fullWidth
                >
                  {t('markAsArrived')}
                </Button>
              </Stack>
            </Paper>
          </Stepper.Step>
          <Stepper.Completed>
            <Paper p="md" withBorder>
              <Stack gap="md" align="center">
                <IconCheck size="3rem" color="var(--mantine-color-green-6)" />
                <Text ta="center" size="lg" fw={600}>
                  {t('packageMarkedAsArrived')}
                </Text>
                <Text ta="center" c="dimmed">
                  {t('arrivalCompletionMessage')}
                </Text>
              </Stack>
            </Paper>
          </Stepper.Completed>
        </Stepper>
      </Stack>
    </Modal>
  );
}
