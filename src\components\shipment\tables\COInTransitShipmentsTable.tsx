/* eslint-disable react/require-default-props */
/* eslint-disable max-lines */
/* eslint-disable sonarjs/cognitive-complexity */
import React, {
  useState,
  useRef,
  useMemo,
  useEffect,
} from 'react';
import {
  Paper,
  Group,
  Text,
  Button,
  Stack,
  TextInput,
  Select,
  Box,
  Grid,
  Pagination,
} from '@mantine/core';
import {
  IconSearch,
  IconFilter,
  IconRefresh,
  IconEye,
  IconTruck,
} from '@tabler/icons-react';
import { useDebouncedValue, useMediaQuery } from '@mantine/hooks';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';

import useTranslation from 'next-translate/useTranslation';
import { useIsClient } from '../../../hooks/useIsClient';

import { Shipment, shipmentSortKeysMapping, getMyTransportedShipmentsQuery } from '../../../requests/shipment';
import { DataTableColumn, DataTable } from '../../common/DataTable';
import { StatusBadge } from '../../common/StatusBadge';
import { ShipmentCard } from '../cards';

interface COInTransitShipmentsTableProps {
  onViewShipment?: (shipment: Shipment) => void;
  // Note: Car Operators cannot assign IN_TRANSIT shipments - only destination AO can
}

const ITEMS_PER_PAGE = 10;

const getSizeLabel = (size: string, t: (key: string) => string) => {
  switch (size) {
    case 'SMALL': return t('sizeSmallLabel');
    case 'MEDIUM': return t('sizeMediumLabel');
    case 'LARGE': return t('sizeLargeLabel');
    case 'EXTRA_LARGE': return t('sizeExtraLargeLabel');
    default: return size;
  }
};

export default function COInTransitShipmentsTable({
  onViewShipment,
}: COInTransitShipmentsTableProps) {
  const { t } = useTranslation('shipments');
  const router = useRouter();

  const isClient = useIsClient();
  const isDesktop = useMediaQuery('(min-width: 992px)');
  const isRTL = router.locale === 'ar';
  // Note: CO users don't need profile data for filtering - they see all IN_TRANSIT shipments
  const formatDate = (dateString: string) => {
    if (!isClient) return 'Loading...';
    try { return new Date(dateString).toLocaleDateString(); } catch { return 'Invalid date'; }
  };
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(ITEMS_PER_PAGE);
  const [searchInput, setSearchInput] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>('IN_TRANSIT');
  const [debouncedSearchValue] = useDebouncedValue(searchInput, 500);
  const searchInputRef = useRef<HTMLInputElement>(null);
  useEffect(() => {
    const { status } = router.query;
    if (status && typeof status === 'string') setStatusFilter(status);
  }, [router.query]);
  const handleInputChange = (value: string) => { setSearchInput(value); setPage(1); };
  const filters = useMemo(() => {
    const searchValue = debouncedSearchValue.trim();
    // For CO users, don't filter by access points - they should see all IN_TRANSIT shipments
    return {
      search: searchValue,
      status: statusFilter || 'IN_TRANSIT'
    };
  }, [debouncedSearchValue, statusFilter]);
  const sortObj = useMemo(() => ({
    sortBy: shipmentSortKeysMapping.has('createdAt')
      ? shipmentSortKeysMapping.get('createdAt')
      : 'createdAt',
    sortOrder: 'desc' as 'asc' | 'desc',
  }), []);
  const {
    data: shipmentsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    ...getMyTransportedShipmentsQuery({
      pagination: { page: page - 1, pageSize },
      filters,
      sort: `${sortObj.sortBy}:${sortObj.sortOrder}`,
    }),
    refetchOnWindowFocus: false,
    staleTime: 0,
    gcTime: 0,
    refetchOnMount: true,
    retry: 1,
    retryDelay: 1000,
  });
  const shipments = shipmentsData?.data?.shipments || [];
  const totalCount = shipmentsData?.data?.pagination?.total || 0;
  const totalPages = Math.ceil(totalCount / pageSize);
  const columns: DataTableColumn<Shipment>[] = [
    {
      label: 'Shipment ID',
      accessor: 'id',
      render: (shipment) => (
        <Text size="sm" fw={500}>
          {`#${shipment.id.slice(-8).toUpperCase()}`}
        </Text>
      ),
      sortable: true,
    },
    {
      label: 'Status',
      accessor: 'status',
      render: (shipment) => <StatusBadge status={shipment.status} />,
      sortable: true,
    },
    {
      label: 'Weight & Size',
      accessor: 'weight',
      render: (shipment) => (
        <Text size="sm">
          {shipment.weight}
          {'kg • '}
          {getSizeLabel(shipment.size, t)}
        </Text>
      ),
    },
    {
      label: t('destinationAO'),
      accessor: 'destAoId',
      render: (shipment) => (
        <Text size="sm">
          {shipment.destAoId ? shipment.destAoId : t('notAssigned')}
        </Text>
      ),
    },
    {
      label: t('inTransitSince'),
      accessor: 'updatedAt',
      render: (shipment) => (
        <Text size="sm">
          {formatDate(shipment.updatedAt)}
        </Text>
      ),
      sortable: true,
    },
  ];
  const rowActions = (shipment: Shipment) => (
    <Group gap="xs">
      <Button
        variant="light"
        size="xs"
        leftSection={<IconEye size="1rem" />}
        onClick={() => onViewShipment?.(shipment)}
      >
        {t('viewButton')}
      </Button>
      {/* Car Operators cannot assign IN_TRANSIT shipments - only destination AO can */}
    </Group>
  );
  if (!isClient) {
    return (
      <Stack gap="lg">
        <Paper p="md" withBorder>
          <Box h={60} bg="gray.1" style={{ borderRadius: 'var(--mantine-radius-sm)' }} />
        </Paper>
        <Paper withBorder>
          <Box h={400} bg="gray.1" style={{ borderRadius: 'var(--mantine-radius-sm)' }} />
        </Paper>
      </Stack>
    );
  }
  return (
    <Stack gap="lg">
      <Paper p="md" withBorder>
        <Group justify="space-between" mb="md">
          {isRTL && (
            <Button
              leftSection={<IconRefresh size="1rem" />}
              variant="light"
              onClick={() => refetch()}
              loading={isLoading}
            >
              {t('refresh')}
            </Button>
          )}
          <Group gap="md">
            <IconTruck size="1.5rem" color="cyan" />
            <div>
              <Text size="lg" fw={600}>{t('inTransitShipmentsTitle')}</Text>
              <Text size="sm" c="dimmed">{t('inTransitShipmentsDescription')}</Text>
            </div>
          </Group>
          {!isRTL && (
            <Button
              leftSection={<IconRefresh size="1rem" />}
              variant="light"
              onClick={() => refetch()}
              loading={isLoading}
            >
              {t('refresh')}
            </Button>
          )}
        </Group>
        <Group gap="lg">
          <div>
            <Text size="xl" fw={700} c="cyan">{totalCount}</Text>
            <Text size="sm" c="dimmed">{t('inTransitCount')}</Text>
          </div>
        </Group>
      </Paper>
      <Paper p="md" withBorder>
        <Group gap="md" align="flex-end">
          <TextInput
            ref={searchInputRef}
            placeholder={t('searchShipments')}
            leftSection={<IconSearch size="1rem" />}
            value={searchInput}
            onChange={(e) => handleInputChange(e.currentTarget.value)}
            style={{ flex: 1 }}
          />
          <Select
            placeholder={t('filterByStatus')}
            leftSection={<IconFilter size="1rem" />}
            data={[
              { value: 'IN_TRANSIT', label: t('statusInTransit') },
              { value: 'ARRIVED_AT_DESTINATION', label: t('statusArrivedAtDestination') },
              { value: 'PENDING', label: t('statusPending') },
              { value: 'AWAITING_PICKUP', label: t('statusAwaitingPickup') },
              { value: 'DELIVERED', label: t('statusDelivered') },
              { value: 'CANCELLED', label: t('statusCancelled') },
              { value: 'EXPIRED', label: t('statusExpired') },
            ]}
            value={statusFilter}
            // eslint-disable-next-line max-len
            onChange={(value) => { setPage(1); setStatusFilter(value || 'IN_TRANSIT'); const newQuery = { ...router.query }; if (value && value !== 'IN_TRANSIT') { newQuery.status = value; } else { delete newQuery.status; } router.replace({ pathname: router.pathname, query: newQuery }, undefined, { shallow: true }); }}
            clearable
            style={{ minWidth: 200 }}
          />
          <Select
            placeholder={t('itemsPerPagePlaceholder')}
            data={[
              { value: '10', label: t('perPage10') },
              { value: '25', label: t('perPage25') },
              { value: '50', label: t('perPage50') },
            ]}
            value={pageSize.toString()}
            onChange={(newPageSize) => { if (newPageSize) { setPageSize(parseInt(newPageSize, 10)); setPage(1); } }}
            style={{ minWidth: 150 }}
          />
        </Group>
      </Paper>
      {isDesktop ? (
        <DataTable<Shipment>
          columns={columns}
          data={shipments}
          loading={isLoading}
          error={error instanceof Error ? error.message : undefined}
          emptyMessage={totalCount === 0 ? t('noShipmentsInTransit') : t('noShipmentsMatchFilters')}
          pagination={{ page, totalPages, onPageChange: setPage }}
          rowActions={rowActions}
        />
      ) : (
        <>
          <Grid>
            {shipments.map((shipment: Shipment) => (
              <Grid.Col key={shipment.id} span={12}>
                <ShipmentCard
                  shipment={shipment}
                  hideDefaultActions
                  extraActions={rowActions(shipment)}
                />
              </Grid.Col>
            ))}
          </Grid>
          {totalPages > 1 && (
            <Pagination value={page} total={totalPages} onChange={setPage} size="sm" />
          )}
        </>
      )}
      <Text c="dimmed" size="sm" ta="center">
        <span>
          {t('showing')}
          {' '}
        </span>
        <span>{shipments.length}</span>
        <span>
          {' '}
          {t('of')}
          {' '}
        </span>
        <span>{totalCount}</span>
        <span>
          {' '}
          {t('inTransitShipmentsLowercase')}
        </span>
        {(searchInput || statusFilter !== 'IN_TRANSIT') && (
          <span>
            {' '}
            {t('withCurrentFilters')}
          </span>
        )}
      </Text>
    </Stack>
  );
}
