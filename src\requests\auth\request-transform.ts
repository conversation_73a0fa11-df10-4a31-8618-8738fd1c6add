/* eslint-disable sonarjs/no-duplicate-string */
import { z } from 'zod';

// Common validation patterns
const VALIDATION = {
  PASSWORD: {
    MIN_LENGTH: 8,
    PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    MESSAGE: 'Password must have 8+ chars with uppercase, lowercase, number and special char',
  },
  PHONE: {
    PATTERN: /^(\+\d{1,4}\s?)?\d{7,15}$/,
    MESSAGE: 'Phone number must be a valid international number with 7-15 digits',
  },
  NAME: {
    MIN_LENGTH: 2,
    MESSAGE: 'Name must be at least 2 characters',
  },
};

// User types
export const USER_TYPES = ['CUSTOMER', 'ACCESS_OPERATOR', 'CAR_OPERATOR', 'ADMIN'] as const;
export type UserType = typeof USER_TYPES[number];

// Base schemas
export const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

export const passwordSchema = z.string()
  .min(VALIDATION.PASSWORD.MIN_LENGTH, `Password must be at least ${VALIDATION.PASSWORD.MIN_LENGTH} characters`)
  .regex(VALIDATION.PASSWORD.PATTERN, VALIDATION.PASSWORD.MESSAGE);

// Common user fields used in registration (frontend camelCase)
const userBaseSchema = z.object({
  name: z.string().min(VALIDATION.NAME.MIN_LENGTH, VALIDATION.NAME.MESSAGE),
  email: z.string().email('Invalid email format'),
  password: passwordSchema,
  phone: z.string().regex(VALIDATION.PHONE.PATTERN, VALIDATION.PHONE.MESSAGE),
  userType: z.enum(USER_TYPES),
});

// Registration schema - includes optional fields for backend compatibility (frontend camelCase)
// Uses placeholder values that will be updated during profile completion
export const registrationApiRequestSchema = userBaseSchema.extend({
  // Access Operator optional fields
  businessName: z.string().optional(),
  address: z.string().optional(),
  geoLatitude: z.number().optional(),
  geoLongitude: z.number().optional(),

  // Car Operator optional fields
  licenseNumber: z.string().optional(),
  vehicleInfo: z.string().optional(),
});

// Backend registration schema - transforms camelCase to snake_case
export const registrationBackendRequestSchema = registrationApiRequestSchema.transform((data) => ({
  name: data.name,
  email: data.email,
  password: data.password,
  phone: data.phone,
  user_type: data.userType,
  // Access Operator optional fields
  business_name: data.businessName,
  address: data.address,
  geo_latitude: data.geoLatitude,
  geo_longitude: data.geoLongitude,
  // Car Operator optional fields
  license_number: data.licenseNumber,
  vehicle_info: data.vehicleInfo,
}));

// Legacy export for backward compatibility
export const registrationSchema = registrationApiRequestSchema;

// Password management schemas
export const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email format'),
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  password: passwordSchema,
  confirmPassword: passwordSchema,
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
  confirmPassword: passwordSchema,
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

export const verifyEmailSchema = z.object({
  token: z.string().min(1, 'Token is required'),
});

// OTP verification schemas
export const sendVerificationOtpSchema = z.object({
  email: z.string().email('Invalid email format'),
});

export const verifyEmailOtpSchema = z.object({
  // email: z.string().email('Invalid email format'),
  otp: z.string().regex(/^\d{6}$/, 'OTP must be 6 digits'),
});

export const resendVerificationOtpSchema = z.object({
  email: z.string().email('Invalid email format'),
});

export const authRequestSchemas = {
  login: loginSchema,
  registration: registrationApiRequestSchema,
  registrationBackend: registrationBackendRequestSchema,
  forgotPassword: forgotPasswordSchema,
  resetPassword: resetPasswordSchema,
  changePassword: changePasswordSchema,
  verifyEmail: verifyEmailSchema,
  sendVerificationOtp: sendVerificationOtpSchema,
  verifyEmailOtp: verifyEmailOtpSchema,
  resendVerificationOtp: resendVerificationOtpSchema,
};

export default authRequestSchemas;
