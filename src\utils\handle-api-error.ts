import { LocaleCookie, HTTP_CODE } from '../data';
import { ROUTES, authPages } from '../data/routes';
// Removed unused error type imports
import { notifications } from '@mantine/notifications';
import { AxiosError } from 'axios';
import { getCookie, setCookie } from 'cookies-next';
import { signIn, signOut } from 'next-auth/react';
import getT from 'next-translate/getT';

// Account blocked error handling removed as the routes are not implemented

const handleUnauthorizedCondition = () => {
  if (
    typeof window !== 'undefined'
    && authPages.includes(window.location.pathname)
  ) {
    signIn('keycloak');
  }
  // Iframe handling removed as it's not used in this application
};

const errorCache: { [key: string]: number } = {};

export const handleApiError = async <T extends object>(
  error: AxiosError<{
    message: { fallback: string; key: string; params: T };
    code: number;
  }>,
  isQueryRequest?: boolean,
  withoutNotification?: boolean,
) => {
  const lang = getCookie(LocaleCookie);
  const t = await getT(lang as string, 'error');

  // this is not a pure function and it is ok since it will be used only in the context of this app
  if (error.response?.status === HTTP_CODE.UNAUTHORIZED) {
    // if authorized then logout
    signOut({ redirect: false });
    handleUnauthorizedCondition();
  } else if (error.response && !withoutNotification) {
    const {
      response: {
        data: { message },
        status,
      },
    } = error;

    // Handle 500 errors with proper translation
    let errorMessage: string;
    let cacheKey: string;

    if (status === 500) {
      // For 500 errors, use the internalServerError translation
      const keyTranslate = t('internalServerError');
      errorMessage = keyTranslate !== 'internalServerError' ? keyTranslate : 'Internal server error occurred';
      cacheKey = 'internalServerError';
    } else if (message?.key) {
      // For other errors with message key
      const keyTranslate = t(message.key, { ...message?.params });
      errorMessage = keyTranslate === message.key ? message.fallback : keyTranslate;
      cacheKey = message.key;
    } else {
      // Fallback for errors without proper message structure
      errorMessage = message?.fallback || 'An error occurred';
      cacheKey = 'generalError';
    }

    const canShowNotification = Date.now() - (errorCache[cacheKey ?? ''] ?? 0) > 30 * 1000;

    if (
      typeof window !== 'undefined'
      && canShowNotification
      && isQueryRequest
    ) {
      errorCache[cacheKey || ''] = Date.now();
      notifications.show({
        message: errorMessage,
        color: 'red',
      });
    } else if (!isQueryRequest) {
      notifications.show({
        message: errorMessage,
        color: 'red',
      });
    }
  }
};
